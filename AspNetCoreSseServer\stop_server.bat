@echo off
chcp 65001 >nul
echo 🛑 正在停止心花商城 MCP 服务器...

REM 查找并终止 AspNetCoreSseServer 进程
tasklist /FI "IMAGENAME eq AspNetCoreSseServer.exe" 2>NUL | find /I /N "AspNetCoreSseServer.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo 📋 找到运行中的服务器进程，正在终止...
    taskkill /F /IM AspNetCoreSseServer.exe >NUL 2>&1
    if "%ERRORLEVEL%"=="0" (
        echo ✅ 服务器已成功停止
    ) else (
        echo ❌ 停止服务器时出现错误
    )
) else (
    echo ℹ️  没有找到运行中的服务器进程
)

REM 也尝试终止 dotnet 进程（如果使用 dotnet run 启动）
tasklist /FI "IMAGENAME eq dotnet.exe" /FI "WINDOWTITLE eq *AspNetCoreSseServer*" 2>NUL | find /I /N "dotnet.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo 📋 找到相关的 dotnet 进程，正在终止...
    for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq dotnet.exe" /FO CSV ^| findstr AspNetCoreSseServer') do (
        taskkill /F /PID %%i >NUL 2>&1
    )
)

echo.
echo 🔄 现在可以安全地重新构建和启动服务器了
pause