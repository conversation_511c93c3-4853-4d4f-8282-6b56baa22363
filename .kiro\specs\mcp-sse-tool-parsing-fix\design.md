# Design Document

## Overview

The analysis reveals significant architectural differences between the official ASP.NET Core SSE server sample and the current MCPServer implementation. The official sample uses a dedicated MCP SDK (`ModelContextProtocol.AspNetCore`) with built-in SSE transport, tool registration, and protocol handling, while the current implementation uses a custom-built MCP server with manual JSON-RPC handling.

The core issue preventing successful tool parsing through the SSE endpoint is that the current implementation doesn't properly expose tool definitions during the SSE initialization phase, and the JSON-RPC protocol handling doesn't fully conform to MCP specifications.

## Architecture

### Current Implementation Issues

1. **Missing MCP SDK Integration**: The current implementation manually handles MCP protocol without using the official SDK
2. **Incomplete SSE Tool Discovery**: The SSE endpoint only sends an "endpoint" event but doesn't expose available tools
3. **Custom JSON-RPC Handling**: Manual JSON-RPC implementation may not fully conform to MCP protocol specifications
4. **Tool Registration Mismatch**: Tools are registered using custom attributes instead of MCP SDK attributes
5. **Protocol Version Mismatch**: Current implementation uses different protocol versions and capabilities

### Target Architecture

The solution involves two approaches:

**Option A: Migrate to Official MCP SDK (Recommended)**
- Replace custom MCP implementation with `ModelContextProtocol.AspNetCore` package
- Use official MCP attributes and tool registration
- Leverage built-in SSE transport and protocol handling

**Option B: Fix Current Custom Implementation**
- Enhance SSE handler to properly expose tools during initialization
- Fix JSON-RPC protocol compliance issues
- Improve tool schema serialization and transmission

## Components and Interfaces

### Option A: MCP SDK Migration

#### 1. Package Dependencies
```xml
<PackageReference Include="ModelContextProtocol.AspNetCore" Version="latest" />
<PackageReference Include="Microsoft.Extensions.AI" Version="latest" />
```

#### 2. Tool Registration System
```csharp
// Replace custom attributes with MCP SDK attributes
[McpServerToolType]
public class ProductTools
{
    [McpServerTool, Description("Search for products")]
    public async Task<string> SearchProductsAsync(string query) { ... }
}
```

#### 3. Service Configuration
```csharp
builder.Services.AddMcpServer()
    .WithHttpTransport()
    .WithTools<ProductTools>()
    .WithTools<OrderTools>()
    .WithTools<ShopTools>()
    .WithTools<UserTools>();
```

#### 4. Endpoint Mapping
```csharp
app.MapMcp(); // Replaces custom middleware
```

### Option B: Custom Implementation Enhancement

#### 1. Enhanced SSE Handler
- Add tool discovery during SSE initialization
- Implement proper MCP protocol handshake
- Send tool definitions as part of SSE stream

#### 2. Improved JSON-RPC Protocol Handler
- Fix protocol version compliance (use "2024-11-05" consistently)
- Enhance error handling and response formatting
- Implement proper capabilities negotiation

#### 3. Tool Schema Serialization
- Ensure tool schemas conform to MCP specifications
- Add proper parameter validation and type conversion
- Implement consistent response formatting

## Data Models

### MCP Protocol Messages

#### Initialize Request/Response
```json
{
  "jsonrpc": "2.0",
  "id": "1",
  "method": "initialize",
  "params": {
    "protocolVersion": "2024-11-05",
    "clientInfo": {
      "name": "Client Name",
      "version": "1.0.0"
    }
  }
}
```

#### Tools List Response
```json
{
  "jsonrpc": "2.0",
  "id": "2",
  "result": {
    "tools": [
      {
        "name": "SearchProductsAsync",
        "description": "Search for products",
        "inputSchema": {
          "type": "object",
          "properties": {
            "query": {
              "type": "string",
              "description": "Search query"
            }
          },
          "required": ["query"]
        }
      }
    ]
  }
}
```

#### Tool Call Request/Response
```json
{
  "jsonrpc": "2.0",
  "id": "3",
  "method": "tools/call",
  "params": {
    "name": "SearchProductsAsync",
    "arguments": {
      "query": "laptop"
    }
  }
}
```

### SSE Message Format

#### Endpoint Event
```
event: endpoint
data: /message?sessionId=abc123

```

#### Tool Discovery Event (New)
```
event: tools
data: {"tools": [...]}

```

## Error Handling

### Protocol Error Codes
- `-32700`: Parse error (Invalid JSON)
- `-32600`: Invalid Request (Missing required fields)
- `-32601`: Method not found (Unknown method)
- `-32602`: Invalid params (Invalid parameters)
- `-32603`: Internal error (Server-side errors)

### SSE Connection Handling
- Proper CORS headers for cross-origin requests
- Connection keep-alive with appropriate timeouts
- Graceful handling of client disconnections
- Error logging and recovery mechanisms

### Tool Execution Errors
- Parameter validation and type conversion errors
- Database connection and query errors
- Business logic exceptions
- Timeout handling for long-running operations

## Testing Strategy

### Unit Tests
1. **Tool Registration Tests**
   - Verify tools are properly registered with correct schemas
   - Test parameter validation and type conversion
   - Validate tool execution with various inputs

2. **JSON-RPC Protocol Tests**
   - Test initialize handshake
   - Verify tools/list response format
   - Test tools/call request/response cycle
   - Validate error handling and response codes

3. **SSE Handler Tests**
   - Test SSE connection establishment
   - Verify proper header configuration
   - Test message transmission and formatting
   - Validate session management

### Integration Tests
1. **End-to-End SSE Flow**
   - Connect to SSE endpoint
   - Receive endpoint event
   - Send initialize request
   - Request tools list
   - Execute tool calls
   - Verify response formats

2. **Client Compatibility Tests**
   - Test with FastGPT client
   - Test with other MCP-compatible clients
   - Verify cross-platform compatibility
   - Test various network conditions

### Performance Tests
1. **Concurrent Connections**
   - Multiple SSE connections
   - Concurrent tool executions
   - Memory usage under load
   - Connection cleanup verification

2. **Tool Execution Performance**
   - Database query performance
   - Response time measurements
   - Resource utilization monitoring
   - Scalability testing

## Implementation Approach

### Phase 1: Analysis and Preparation
1. Create comprehensive comparison between current and official implementations
2. Identify all breaking changes required for migration
3. Plan backward compatibility strategy
4. Set up testing infrastructure

### Phase 2: Core Protocol Fixes
1. Fix JSON-RPC protocol compliance issues
2. Enhance SSE tool discovery mechanism
3. Improve error handling and logging
4. Update tool schema serialization

### Phase 3: SDK Migration (Option A) or Enhancement (Option B)
1. **Option A**: Migrate to official MCP SDK
   - Update package references
   - Refactor tool classes with MCP attributes
   - Replace custom middleware with SDK endpoints
   - Update service registration

2. **Option B**: Enhance custom implementation
   - Implement missing protocol features
   - Add proper capabilities negotiation
   - Enhance tool discovery in SSE stream
   - Improve protocol version handling

### Phase 4: Testing and Validation
1. Comprehensive testing with multiple clients
2. Performance benchmarking
3. Security validation
4. Documentation updates

## Security Considerations

### CORS Configuration
- Proper Access-Control headers for cross-origin requests
- Whitelist specific origins in production
- Validate request origins and methods

### Input Validation
- Sanitize all tool parameters
- Validate JSON-RPC message structure
- Prevent injection attacks in database queries
- Rate limiting for tool executions

### Authentication and Authorization
- Consider adding authentication for production use
- Implement role-based access control for tools
- Secure database connection strings
- Audit logging for tool executions

## Performance Optimization

### Connection Management
- Efficient SSE connection pooling
- Proper connection cleanup and resource disposal
- Optimize keep-alive intervals
- Monitor connection metrics

### Database Operations
- Use connection pooling for database access
- Implement query optimization
- Add caching for frequently accessed data
- Monitor database performance metrics

### Memory Management
- Proper disposal of resources
- Optimize JSON serialization/deserialization
- Monitor memory usage patterns
- Implement garbage collection optimization