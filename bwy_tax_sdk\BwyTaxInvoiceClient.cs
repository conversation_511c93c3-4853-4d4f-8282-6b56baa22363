using System;
using System.Threading.Tasks;

namespace BwyTax.Sdk
{
    /// <summary>
    /// 百望云电子发票客户端
    /// </summary>
    public class BwyTaxInvoiceClient
    {
        private readonly string _apiBaseUrl;
        private readonly string _accessKey;
        private readonly string _secretKey;
        private readonly string _sellerTaxNo;
        private readonly string _terminalCode;
        private readonly ILogger _logger;
        private readonly InvoiceApiHelper _apiHelper;
        
        /// <summary>
        /// 初始化百望云电子发票客户端
        /// </summary>
        /// <param name="apiBaseUrl">API服务地址，例如：http://baishanyunpiao.com/prod-api</param>
        /// <param name="accessKey">应用ID</param>
        /// <param name="secretKey">应用密钥</param>
        /// <param name="sellerTaxNo">销方税号</param>
        /// <param name="terminalCode">开票终端代码（可选）</param>
        /// <param name="logger">日志记录器（可选）</param>
        public BwyTaxInvoiceClient(
            string apiBaseUrl, 
            string accessKey, 
            string secretKey, 
            string sellerTaxNo, 
            string terminalCode = "", 
            ILogger? logger = null)
        {
            _apiBaseUrl = apiBaseUrl;
            _accessKey = accessKey;
            _secretKey = secretKey;
            _sellerTaxNo = sellerTaxNo;
            _terminalCode = terminalCode;
            _logger = logger ?? new ConsoleLogger();
            _apiHelper = new InvoiceApiHelper(apiBaseUrl, accessKey, secretKey, _logger);
        }
        
        /// <summary>
        /// 获取访问令牌
        /// </summary>
        /// <returns>令牌响应</returns>
        public async Task<TokenResponse> GetTokenAsync()
        {
            return await _apiHelper.GetTokenAsync();
        }
        
        /// <summary>
        /// 开具电子发票（V2版本接口）
        /// </summary>
        /// <param name="invoiceData">发票数据</param>
        /// <param name="isSplit">是否超限拆分</param>
        /// <param name="isReturnRedInfo">是否返回蓝票已红冲信息</param>
        /// <param name="formatGenerate">是否生成版式并返回链接</param>
        /// <param name="isTest">是否使用税控测试环境</param>
        /// <returns>开票结果</returns>
        public async Task<InvoiceResultV3> IssueInvoiceAsync(
            InvoiceDataV2 invoiceData,
            bool isSplit = false,
            bool isReturnRedInfo = true,
            bool formatGenerate = true,
            bool isTest = false)
        {
            // 获取Token
            var tokenResponse = await GetTokenAsync();
            if (!tokenResponse.State)
            {
                throw new Exception($"获取访问令牌失败：{tokenResponse.Message}");
            }
            
            string token = tokenResponse.Data;
            
            // 创建发票请求
            var invoiceRequest = new InvoiceRequestV2
            {
                TaxNo = _sellerTaxNo,
                InvoiceTerminalCode = _terminalCode,
                IsSplit = isSplit,
                IsReturnRedInfo = isReturnRedInfo ? "1" : "0",
                FormatGenerate = formatGenerate,
                Data = invoiceData
            };
            
            // 调用开票API
            return await _apiHelper.IssueInvoiceV2Async(invoiceRequest, token, isTest);
        }

        /// <summary>
        /// 快速开具电子发票
        /// </summary>
        /// <param name="invoiceTypeCode">发票类型代码（02全电普票、01全电专票等）</param>
        /// <param name="buyerName">购方名称</param>
        /// <param name="buyerTaxNo">购方税号（可选）</param>
        /// <param name="goodsList">商品明细列表</param>
        /// <param name="drawer">开票人</param>
        /// <param name="checker">复核人</param>
        /// <param name="payee">收款人</param>
        /// <param name="sellerBankName">销方银行名称（可选）</param>
        /// <param name="sellerBankNumber">销方银行账号（可选）</param>
        /// <param name="remarks">备注（可选）</param>
        /// <param name="isTest">是否使用税控测试环境</param>
        /// <param name="buyerAddressPhone">购买方电话</param>
        /// <param name="buyerEmail">购买方邮箱</param>
        /// <returns>开票结果</returns>
        public async Task<InvoiceResultV3> QuickIssueInvoiceAsync(
            string invoiceTypeCode,
            string buyerName,
            string? buyerTaxNo,
            InvoiceItem[] goodsList,
            string drawer,
            string checker,
            string payee,
            string? sellerBankName = null,
            string? sellerBankNumber = null,
            string? remarks = null,
            bool isTest = false,
            string? buyerAddressPhone=null,
            string? buyerEmail=null
            )
        {
            // 生成唯一流水号
            string serialNumber = DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999);
            
            // 创建发票数据
            var invoiceData = new InvoiceDataV2
            {
                InvoiceTypeCode = invoiceTypeCode,
                InvoiceType = "0", // 蓝票
                SerialNo = serialNumber,
                BuyerName = buyerName,
                BuyerTaxNo = buyerTaxNo,
                SellerBankName = sellerBankName,
                SellerBankNumber = sellerBankNumber,
                PriceTaxMark = 1, // 含税
                Drawer = drawer,
                Checker = checker,
                Payee = payee,
                Remarks = remarks,
                //BuyerAddressPhone = buyerAddressPhone,
                BuyerTelphone = buyerAddressPhone,
                //BuyerPhone = buyerAddressPhone,
                EmailCarbonCopy = buyerEmail,
                BuyerEmail = buyerEmail,
                InvoiceDetailsList = new System.Collections.Generic.List<InvoiceDetailV2>()
            };
            
            // 添加商品明细
            int lineNo = 1;
            foreach (var item in goodsList)
            {
                invoiceData.InvoiceDetailsList.Add(new InvoiceDetailV2
                {
                    GoodsLineNo = lineNo++,
                    GoodsCode = item.GoodsCode,
                    GoodsName = item.GoodsName,
                    GoodsQuantity = item.GoodsQuantity,
                    GoodsPrice = item.GoodsPrice,
                    GoodsTotalPrice = item.GoodsTotalPrice,
                    GoodsTaxRate = item.GoodsTaxRate,
                    GoodsTotalTax = item.GoodsTotalTax,
                    Unit = item.Unit,
                    SpecificationModel = item.SpecificationModel,
                    InvoiceLineNature = "0" // 正常行性质
                });
            }
            
            // 调用开票API
            return await IssueInvoiceAsync(invoiceData, false, true, true, isTest);
        }

        /// <summary>
        /// 版式文件生成
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task<InvoiceCreateResponseData> InvoiceCreateAsync(InvoiceCreateDto dto) 
        {
            // 获取Token
            var tokenResponse = await GetTokenAsync();
            if (!tokenResponse.State)
            {
                throw new Exception($"获取访问令牌失败：{tokenResponse.Message}");
            }

            string token = tokenResponse.Data;

            dto.TaxNo = _sellerTaxNo;
            // 调用开票API
            return await _apiHelper.createInvoiceV2Async(dto, token);
        }
    }
} 