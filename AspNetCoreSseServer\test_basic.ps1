# Basic AspNetCore SSE Server Test
Write-Host "=== Basic AspNetCore SSE Server Test ===" -ForegroundColor Green

$serverUrl = "http://localhost:3001"

# Function to test if server is running
function Test-ServerRunning {
    try {
        $response = Invoke-WebRequest -Uri "$serverUrl/sse" -Method GET -TimeoutSec 5
        return $true
    }
    catch {
        return $false
    }
}

# Function to make MCP requests
function Invoke-McpRequest {
    param(
        [string]$Method,
        [hashtable]$Params = @{},
        [string]$Id = "1"
    )
    
    $jsonRpcRequest = @{
        jsonrpc = "2.0"
        id = $Id
        method = $Method
        params = $Params
    } | ConvertTo-Json -Depth 5
    
    try {
        $response = Invoke-WebRequest -Uri "$serverUrl" -Method POST -Body $jsonRpcRequest -ContentType "application/json" -TimeoutSec 15
        return $response.Content | ConvertFrom-Json
    }
    catch {
        Write-Host "Request Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Check if server is running
Write-Host "`n1. Checking if server is running..." -ForegroundColor Yellow
if (Test-ServerRunning) {
    Write-Host "✅ Server is running on $serverUrl" -ForegroundColor Green
} else {
    Write-Host "❌ Server is not running. Starting server..." -ForegroundColor Red
    Write-Host "Please run: dotnet run in another terminal" -ForegroundColor Yellow
    Write-Host "Then run this test again." -ForegroundColor Yellow
    exit 1
}

# Test MCP initialization
Write-Host "`n2. Testing MCP initialization..." -ForegroundColor Yellow
$initResponse = Invoke-McpRequest -Method "initialize" -Params @{
    protocolVersion = "2024-11-05"
    capabilities = @{
        tools = @{}
    }
    clientInfo = @{
        name = "test-client"
        version = "1.0.0"
    }
}

if ($initResponse -and $initResponse.result) {
    Write-Host "✅ MCP initialization successful" -ForegroundColor Green
} else {
    Write-Host "❌ MCP initialization failed" -ForegroundColor Red
    exit 1
}

# Test tools list
Write-Host "`n3. Testing tools list..." -ForegroundColor Yellow
$toolsResponse = Invoke-McpRequest -Method "tools/list"

if ($toolsResponse -and $toolsResponse.result -and $toolsResponse.result.tools) {
    $toolCount = $toolsResponse.result.tools.Count
    Write-Host "✅ Found $toolCount tools:" -ForegroundColor Green
    $toolsResponse.result.tools | ForEach-Object {
        Write-Host "   - $($_.name)" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Failed to get tools list" -ForegroundColor Red
    exit 1
}

# Test simple calculator
Write-Host "`n4. Testing Calculator tool..." -ForegroundColor Yellow
$calcResponse = Invoke-McpRequest -Method "tools/call" -Params @{
    name = "Add"
    arguments = @{
        a = 15
        b = 25
    }
}

if ($calcResponse -and $calcResponse.result) {
    Write-Host "✅ Calculator: 15 + 25 = $($calcResponse.result.content[0].text)" -ForegroundColor Green
} else {
    Write-Host "❌ Calculator test failed" -ForegroundColor Red
}

# Test echo tool
Write-Host "`n5. Testing Echo tool..." -ForegroundColor Yellow
$echoResponse = Invoke-McpRequest -Method "tools/call" -Params @{
    name = "Echo"
    arguments = @{
        message = "Test message"
    }
}

if ($echoResponse -and $echoResponse.result) {
    Write-Host "✅ Echo: $($echoResponse.result.content[0].text)" -ForegroundColor Green
} else {
    Write-Host "❌ Echo test failed" -ForegroundColor Red
}

Write-Host "`n=== Basic Test Complete ===" -ForegroundColor Green
Write-Host "Server is working correctly!" -ForegroundColor Green