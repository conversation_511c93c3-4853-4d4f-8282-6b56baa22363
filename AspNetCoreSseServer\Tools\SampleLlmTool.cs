﻿using Microsoft.Extensions.AI;
using ModelContextProtocol.Server;
using System.ComponentModel;

namespace TestServerWithHosting.Tools;

/// <summary>
/// This tool uses dependency injection and async method
/// </summary>
[McpServerToolType]
public sealed class SampleLlmTool
{
    [McpServerTool(Name = "sampleLLM"), Description("使用MCP的采样功能从LLM获取响应")]
    public static async Task<string> SampleLLM(
        IMcpServer thisServer,
        [Description("发送给LLM的提示词")] string prompt,
        [Description("生成的最大令牌数")] int maxTokens,
        CancellationToken cancellationToken)
    {
        ChatOptions options = new()
        {
            Instructions = "你是一个有用的测试服务器助手。",
            MaxOutputTokens = maxTokens,
            Temperature = 0.7f,
        };

        var samplingResponse = await thisServer.AsSamplingChatClient().GetResponseAsync(prompt, options, cancellationToken);

        return $"LLM采样结果: {samplingResponse}";
    }
}
