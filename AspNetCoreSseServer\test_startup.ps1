# 测试服务器启动
Write-Host "测试心花商城 MCP 服务器启动..." -ForegroundColor Green

# 检查端口是否被占用
$portInUse = Get-NetTCPConnection -LocalPort 5000 -ErrorAction SilentlyContinue
if ($portInUse) {
    Write-Host "端口 5000 已被占用，正在尝试停止现有进程..." -ForegroundColor Yellow
    Get-Process -Name "AspNetCoreSseServer" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
}

# 启动服务器（后台运行）
Write-Host "启动服务器..." -ForegroundColor Yellow
$serverProcess = Start-Process -FilePath "dotnet" -ArgumentList "run --urls http://localhost:5000" -PassThru -WindowStyle Hidden

# 等待服务器启动
Write-Host "等待服务器启动..." -ForegroundColor Yellow
$timeout = 30
$elapsed = 0
$serverReady = $false

while ($elapsed -lt $timeout -and -not $serverReady) {
    Start-Sleep -Seconds 1
    $elapsed++
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:5000" -Method GET -TimeoutSec 2 -ErrorAction Stop
        $serverReady = $true
        Write-Host "服务器启动成功!" -ForegroundColor Green
        Write-Host "服务器信息: $($response.name) v$($response.version)" -ForegroundColor Cyan
    } catch {
        # 继续等待
    }
    
    if ($elapsed % 5 -eq 0) {
        Write-Host "已等待 $elapsed 秒..." -ForegroundColor Gray
    }
}

if (-not $serverReady) {
    Write-Host "服务器启动超时!" -ForegroundColor Red
    if ($serverProcess -and -not $serverProcess.HasExited) {
        $serverProcess.Kill()
    }
    exit 1
}

# 运行快速测试
Write-Host ""
Write-Host "运行快速测试..." -ForegroundColor Yellow
& ".\quick_test.ps1"

# 停止服务器
Write-Host ""
Write-Host "停止测试服务器..." -ForegroundColor Yellow
if ($serverProcess -and -not $serverProcess.HasExited) {
    $serverProcess.Kill()
    Write-Host "服务器已停止" -ForegroundColor Green
}

Write-Host ""
Write-Host "启动测试完成!" -ForegroundColor Green