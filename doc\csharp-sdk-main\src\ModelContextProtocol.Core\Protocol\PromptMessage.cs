using Microsoft.Extensions.AI;
using System.Text.Json.Serialization;

namespace ModelContextProtocol.Protocol;

/// <summary>
/// Represents a message within the Model Context Protocol (MCP) system, used for communication between clients and AI models.
/// </summary>
/// <remarks>
/// <para>
/// A <see cref="PromptMessage"/> encapsulates content sent to or received from AI models in the Model Context Protocol.
/// Each message has a specific role (<see cref="Role.User"/> or <see cref="Role.Assistant"/>) and contains content which can be
/// text, images, audio, or embedded resources.
/// </para>
/// <para>
/// This class is similar to <see cref="SamplingMessage"/>, but with enhanced support for embedding resources from the MCP server.
/// It serves as a core data structure in the MCP message exchange flow, particularly in prompt formation and model responses.
/// </para>
/// <para>
/// <see cref="PromptMessage"/> objects are typically used in collections within <see cref="GetPromptResult"/> 
/// to represent complete conversations or prompt sequences. They can be converted to and from <see cref="ChatMessage"/>
/// objects using the extension methods <see cref="AIContentExtensions.ToChatMessage(PromptMessage)"/> and
/// <see cref="AIContentExtensions.ToPromptMessages(ChatMessage)"/>.
/// </para>
/// <para>
/// See the <see href="https://github.com/modelcontextprotocol/specification/blob/main/schema/">schema</see> for details.
/// </para>
/// </remarks>
public sealed class PromptMessage
{
    /// <summary>
    /// Gets or sets the content of the message, which can be text, image, audio, or an embedded resource.
    /// </summary>
    /// <remarks>
    /// The <see cref="Content"/> object contains all the message payload, whether it's simple text,
    /// base64-encoded binary data (for images/audio), or a reference to an embedded resource.
    /// The <see cref="ContentBlock.Type"/> property indicates the specific content type.
    /// </remarks>
    [JsonPropertyName("content")]
    public ContentBlock Content { get; set; } = new TextContentBlock { Text = "" };

    /// <summary>
    /// Gets or sets the role of the message sender, specifying whether it's from a "user" or an "assistant".
    /// </summary>
    /// <remarks>
    /// In the Model Context Protocol, each message must have a clear role assignment to maintain
    /// the conversation flow. User messages represent queries or inputs from users, while assistant
    /// messages represent responses generated by AI models.
    /// </remarks>
    [JsonPropertyName("role")]
    public Role Role { get; set; } = Role.User;
}
