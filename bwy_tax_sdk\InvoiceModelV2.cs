using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace BwyTax.Sdk
{
    /// <summary>
    /// 百望云电子发票请求（2.0版本）
    /// </summary>
    public class InvoiceRequestV2
    {
        /// <summary>
        /// 销方税号
        /// </summary>
        [JsonProperty("taxNo")]
        public string TaxNo { get; set; }

        /// <summary>
        /// 登录名（一税号多账户时使用）
        /// </summary>
        [JsonProperty("taxUserName")]
        public string TaxUserName { get; set; }

        /// <summary>
        /// 开票终端代码（UKey/盘号等）
        /// </summary>
        [JsonProperty("invoiceTerminalCode")]
        public string InvoiceTerminalCode { get; set; }

        /// <summary>
        /// 是否超限拆分（仅 004/007/026）
        /// </summary>
        [JsonProperty("isSplit")]
        public bool? IsSplit { get; set; }

        /// <summary>
        /// 组织机构编码
        /// </summary>
        [JsonProperty("orgCode")]
        public string OrgCode { get; set; }

        /// <summary>
        /// 是否异步 0-同步 1-异步
        /// </summary>
        [JsonProperty("isAsync")]
        public string IsAsync { get; set; }

        /// <summary>
        /// 是否返回蓝票已红冲信息 1-返回
        /// </summary>
        [JsonProperty("isReturnRedInfo")]
        public string IsReturnRedInfo { get; set; }

        /// <summary>
        /// 是否生成版式并返回链接
        /// </summary>
        [JsonProperty("formatGenerate")]
        public bool? FormatGenerate { get; set; }

        /// <summary>
        /// 版式生成是否推送
        /// </summary>
        [JsonProperty("formatPushType")]
        public bool? FormatPushType { get; set; }

        /// <summary>
        /// 发票数据
        /// </summary>
        [JsonProperty("data")]
        public InvoiceDataV2 Data { get; set; }
    }

    /// <summary>
    /// 发票数据（2.0版本）
    /// </summary>
    public class InvoiceDataV2
    {
        /// <summary>
        /// 发票类型代码
        /// 004专 · 007普 · 026电 · 025卷 · 028电专 · 01全电专 · 02全电普
        /// </summary>
        [JsonProperty("invoiceTypeCode")]
        public string InvoiceTypeCode { get; set; }

        /// <summary>
        /// 发票类型 0-蓝票 1-红票
        /// </summary>
        [JsonProperty("invoiceType")]
        public string InvoiceType { get; set; }

        /// <summary>
        /// 开票流水号（幂等关键）
        /// </summary>
        [JsonProperty("serialNo")]
        public string SerialNo { get; set; }

        /// <summary>
        /// 购方名称
        /// </summary>
        [JsonProperty("buyerName")]
        public string BuyerName { get; set; }

        /// <summary>
        /// 购方税号
        /// </summary>
        [JsonProperty("buyerTaxNo")]
        public string BuyerTaxNo { get; set; }

        /// <summary>
        /// 购方地址电话
        /// </summary>
        [JsonProperty("buyerAddressPhone")]
        public string BuyerAddressPhone { get; set; }

        /// <summary>
        /// 购方银行账号
        /// </summary>
        [JsonProperty("buyerBankAccount")]
        public string BuyerBankAccount { get; set; }

        /// <summary>
        /// 商品明细列表
        /// </summary>
        [JsonProperty("invoiceDetailsList")]
        public List<InvoiceDetailV2> InvoiceDetailsList { get; set; }

        /// <summary>
        /// 含税标志， 0：不含税；1：含税（默认不含税）
        /// </summary>
        [JsonProperty("priceTaxMark")]
        public int PriceTaxMark { get; set; }

        /// <summary>
        /// 合计金额
        /// </summary>
        [JsonProperty("invoiceTotalPrice")]
        public decimal? InvoiceTotalPrice { get; set; }

        /// <summary>
        /// 合计税额
        /// </summary>
        [JsonProperty("invoiceTotalTax")]
        public decimal? InvoiceTotalTax { get; set; }

        /// <summary>
        /// 价税合计
        /// </summary>
        [JsonProperty("invoiceTotalPriceTax")]
        public decimal? InvoiceTotalPriceTax { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonProperty("remarks")]
        public string Remarks { get; set; }

        /// <summary>
        /// 红字信息表编号（税控用红冲时必填）
        /// </summary>
        [JsonProperty("redInfoNo")]
        public string RedInfoNo { get; set; }

        /// <summary>
        /// 确认单UUID（全电用红冲时必填）
        /// </summary>
        [JsonProperty("redConfirmUuid")]
        public string RedConfirmUuid { get; set; }

        /// <summary>
        /// 销方名称（可选字段）
        /// </summary>
        [JsonProperty("sellerName")]
        public string SellerName { get; set; }

        /// <summary>
        /// 销方地址电话（可选字段）
        /// </summary>
        [JsonProperty("sellerAddressPhone")]
        public string SellerAddressPhone { get; set; }

        /// <summary>
        /// 销方银行账号（可选字段）
        /// </summary>
        [JsonProperty("sellerBankAccount")]
        public string SellerBankAccount { get; set; }
        /// <summary>
        /// 销方银行名称
        /// </summary>
        [JsonProperty("sellerBankName")]
        public string SellerBankName { get; set; }
        /// <summary>
        /// 销方银行账号
        /// </summary>
        [JsonProperty("sellerBankNumber")]
        public string SellerBankNumber { get; set; }

        /// <summary>
        /// 开票人
        /// </summary>
        [JsonProperty("drawer")]
        public string Drawer { get; set; }

        /// <summary>
        /// 复核人
        /// </summary>
        [JsonProperty("checker")]
        public string Checker { get; set; }

        /// <summary>
        /// 收款人
        /// </summary>
        [JsonProperty("payee")]
        public string Payee { get; set; }
        /// <summary>
        /// 购买方邮箱
        /// </summary>
        [JsonProperty("buyerEmail")]
        public string BuyerEmail { get; set; }
        /// <summary>
        /// 购买方电话
        /// </summary>
        [JsonProperty("buyerTelphone")]
        public string BuyerTelphone { get; set; }
        /// <summary>
        /// 客户电话
        /// </summary>
        [JsonProperty("buyerPhone")]
        public string BuyerPhone { get; set; }
        /// <summary>
        /// 抄送人邮箱 抄送人邮箱,多个用英文逗号隔开,最多 5 个抄送人信息 全电字符长度为 200
        /// </summary>
        [JsonProperty("emailCarbonCopy")]
        public string EmailCarbonCopy { get; set; }

    }

    /// <summary>
    /// 发票明细（2.0版本）
    /// </summary>
    public class InvoiceDetailV2
    {
        /// <summary>
        /// 明细行号（连续、从 1 开始）
        /// </summary>
        [JsonProperty("goodsLineNo")]
        public int GoodsLineNo { get; set; }

        /// <summary>
        /// 税收分类编码（末级）
        /// </summary>
        [JsonProperty("goodsCode")]
        public string GoodsCode { get; set; }

        /// <summary>
        /// 商品名称（与编码匹配）
        /// </summary>
        [JsonProperty("goodsName")]
        public string GoodsName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [JsonProperty("specificationModel")]
        public string SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [JsonProperty("unit")]
        public string Unit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [JsonProperty("goodsQuantity")]
        public decimal? GoodsQuantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [JsonProperty("goodsPrice")]
        public decimal? GoodsPrice { get; set; }

        /// <summary>
        /// 税率，例如0.13
        /// </summary>
        [JsonProperty("goodsTaxRate")]
        public decimal GoodsTaxRate { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [JsonProperty("goodsTotalPrice")]
        public decimal GoodsTotalPrice { get; set; }

        /// <summary>
        /// 含税金额(不含税金额、税额、含税金额任何一个不传时，会根据传入的单价、数量进行计算，可能和实际数值存在误差，建议都传入)
        /// </summary>
        [JsonProperty("goodsTotalPriceTax")]
        public decimal? GoodsTotalPriceTax { get; set; }

        /// <summary>
        /// 税额（若为空自动计算）
        /// </summary>
        [JsonProperty("goodsTotalTax")]
        public decimal? GoodsTotalTax { get; set; }

        /// <summary>
        /// 发票行性质 0-正常 1-折扣 2-被折扣
        /// </summary>
        [JsonProperty("invoiceLineNature")]
        public string InvoiceLineNature { get; set; }

        /// <summary>
        /// 优惠政策标识 0-不使用 1-使用
        /// </summary>
        [JsonProperty("preferentialMarkFlag")]
        public string PreferentialMarkFlag { get; set; }

        /// <summary>
        /// 增值税特殊管理（优惠政策使用时填写）
        /// </summary>
        [JsonProperty("vatSpecialManagement")]
        public string VatSpecialManagement { get; set; }
    }

    /// <summary>
    /// 发票请求响应（2.0版本）
    /// </summary>
    public class InvoiceResponseV2
    {
        /// <summary>
        /// 成功开具的发票信息
        /// </summary>
        [JsonProperty("success")]
        public List<InvoiceResultV2> Success { get; set; }

        /// <summary>
        /// 拆分失败的金额明细
        /// </summary>
        [JsonProperty("fail")]
        public List<InvoiceDetailV2> Fail { get; set; }
    }

    /// <summary>
    /// 开票结果（2.0版本）
    /// </summary>
    public class InvoiceResultV2
    {
        /// <summary>
        /// 发票代码
        /// </summary>
        [JsonProperty("invoiceCode")]
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 发票号码
        /// </summary>
        [JsonProperty("invoiceNo")]
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 开票日期（yyyyMMddHHmmss）
        /// </summary>
        [JsonProperty("invoiceDate")]
        public string InvoiceDate { get; set; }

        /// <summary>
        /// 电子版式下载链接
        /// </summary>
        [JsonProperty("eInvoiceUrl")]
        public string EInvoiceUrl { get; set; }

        /// <summary>
        /// 合计金额
        /// </summary>
        [JsonProperty("invoiceTotalPrice")]
        public decimal InvoiceTotalPrice { get; set; }

        /// <summary>
        /// 合计税额
        /// </summary>
        [JsonProperty("invoiceTotalTax")]
        public decimal InvoiceTotalTax { get; set; }

        /// <summary>
        /// 价税合计
        /// </summary>
        [JsonProperty("invoiceTotalPriceTax")]
        public decimal InvoiceTotalPriceTax { get; set; }

        /// <summary>
        /// 流水号
        /// </summary>
        [JsonProperty("serialNo")]
        public string SerialNo { get; set; }

        /// <summary>
        /// 发票明细
        /// </summary>
        [JsonProperty("invoiceDetailsList")]
        public List<InvoiceDetailV2> InvoiceDetailsList { get; set; }
    }
    public class InvoiceResultV3: InvoiceResponseV2
    {
        public bool state { get; set; }
        public string message { get; set; }
        public object data { get; set; }
    }

    public class InvoiceCreateDto 
    {
        /// <summary>
        /// 销方机构税号
        /// </summary>
        [JsonProperty("taxNo")]
        public string TaxNo { get; set; }
        /// <summary>
        /// 数据对象
        /// </summary>
        [JsonProperty("data")]
        public InvoiceCreateDataDto Data { get; set; }
    }
    public class InvoiceCreateDataDto
    {
        /// <summary>
        /// 发票代码
        /// </summary>
        [JsonProperty("invoiceCode")]
        public string InvoiceCode { get; set; } 
        /// <summary>
        /// 发票号码
        /// </summary>
        [JsonProperty("invoiceNo")]
        public string InvoiceNo { get; set; }
        /// <summary>
        /// 发票请求流水号
        /// </summary>
        [JsonProperty("serialNo")]
        public string SerialNo { get; set; }
        /// <summary>
        /// 手机号码
        /// </summary>
        [JsonProperty("phone")]
        public string Phone { get; set; }
        /// <summary>
        /// 邮箱
        /// </summary>
        [JsonProperty("email")]
        public string Email { get; set; }
        /// <summary>
        /// 邮件抄送地址（多个地址用英文逗号分隔）
        /// </summary>
        [JsonProperty("emailCarbonCopy")]
        public string EmailCarbonCopy { get; set; }
        /// <summary>
        /// 版式通道标识
        /// </summary>
        [JsonProperty("pushType")]
        public string PushType { get; set; }
        /// <summary>
        /// 发票生成模式
        /// </summary>
        [JsonProperty("invoiceIssueMode")]
        public string InvoiceIssueMode { get; set; }
        /// <summary>
        /// 全电发票号码
        /// </summary>
        [JsonProperty("einvoiceNo")]
        public string EinvoiceNo { get; set; } 
    }
    public class InvoiceCreateResponseData
    {
        public bool State { get; set; } // 状态
        public string Message { get; set; } // 消息
        public CreateResponseDetails Data { get; set; } // 数据详情
    }

    public class CreateResponseDetails
    {
        public string Method { get; set; } // 方法
        public bool Success { get; set; } // 是否成功
        public string RequestId { get; set; } // 请求ID
        public InvoiceCreateResponse Response { get; set; } // 响应数据
    }

    public class InvoiceCreateResponse
    {
        public string EInvoiceUrl { get; set; } // 电子发票链接
        public string FileType { get; set; } // 文件类型
    }
}