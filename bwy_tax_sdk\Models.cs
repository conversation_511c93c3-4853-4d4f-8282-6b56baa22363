using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace BwyTax.Sdk
{
    /// <summary>
    /// Token响应类
    /// </summary>
    public class TokenResponse
    {
        [JsonProperty("state")]
        public bool State { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("data")]
        public string Data { get; set; }
    }

    /// <summary>
    /// 发票请求类
    /// </summary>
    public class InvoiceRequest
    {
        /// <summary>
        /// 交易流水号
        /// </summary>
        [JsonProperty("serialNumber")]
        public string SerialNumber { get; set; }

        /// <summary>
        /// 发票类型：1 普通发票，2 专用发票
        /// </summary>
        [JsonProperty("invoiceType")]
        public int InvoiceType { get; set; }

        /// <summary>
        /// 购方信息
        /// </summary>
        [JsonProperty("buyer")]
        public Buyer Buyer { get; set; }

        /// <summary>
        /// 销方信息
        /// </summary>
        [JsonProperty("seller")]
        public Seller Seller { get; set; }

        /// <summary>
        /// 发票明细
        /// </summary>
        [JsonProperty("items")]
        public List<InvoiceItem> Items { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [JsonProperty("totalAmount")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 总税额
        /// </summary>
        [JsonProperty("totalTaxAmount")]
        public decimal TotalTaxAmount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonProperty("remark")]
        public string Remark { get; set; }
    }

    /// <summary>
    /// 购方信息
    /// </summary>
    public class Buyer
    {
        /// <summary>
        /// 购方名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// 购方纳税人识别号
        /// </summary>
        [JsonProperty("taxNumber")]
        public string TaxNumber { get; set; }

        /// <summary>
        /// 购方地址电话
        /// </summary>
        [JsonProperty("addressPhone")]
        public string AddressPhone { get; set; }

        /// <summary>
        /// 购方银行账号
        /// </summary>
        [JsonProperty("bankAccount")]
        public string BankAccount { get; set; }
    }

    /// <summary>
    /// 销方信息
    /// </summary>
    public class Seller
    {
        /// <summary>
        /// 销方名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// 销方纳税人识别号
        /// </summary>
        [JsonProperty("taxNumber")]
        public string TaxNumber { get; set; }

        /// <summary>
        /// 销方地址电话
        /// </summary>
        [JsonProperty("addressPhone")]
        public string AddressPhone { get; set; }

        /// <summary>
        /// 销方银行账号
        /// </summary>
        [JsonProperty("bankAccount")]
        public string BankAccount { get; set; }
    }

    /// <summary>
    /// 发票明细项
    /// </summary>
    public class InvoiceItem
    {
        /// <summary>
        /// 商品名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [JsonProperty("specification")]
        public string Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [JsonProperty("unit")]
        public string Unit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [JsonProperty("quantity")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [JsonProperty("unitPrice")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [JsonProperty("amount")]
        public decimal Amount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [JsonProperty("taxRate")]
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [JsonProperty("taxAmount")]
        public decimal TaxAmount { get; set; }
        
        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string GoodsCode { get; set; } = "";
        
        /// <summary>
        /// 商品名称
        /// </summary>
        public string GoodsName { get; set; } = "";
        
        /// <summary>
        /// 数量
        /// </summary>
        public decimal GoodsQuantity { get; set; } = 1;
        
        /// <summary>
        /// 单价
        /// </summary>
        public decimal GoodsPrice { get; set; }
        
        /// <summary>
        /// 金额
        /// </summary>
        public decimal GoodsTotalPrice { get; set; }
        
        /// <summary>
        /// 税率
        /// </summary>
        public decimal GoodsTaxRate { get; set; }
        
        /// <summary>
        /// 税额
        /// </summary>
        public decimal GoodsTotalTax { get; set; }
        
        /// <summary>
        /// 规格型号（可选）
        /// </summary>
        public string? SpecificationModel { get; set; }
    }

    /// <summary>
    /// 发票响应类
    /// </summary>
    public class InvoiceResponse
    {
        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("data")]
        public InvoiceData Data { get; set; }
    }

    /// <summary>
    /// 发票数据
    /// </summary>
    public class InvoiceData
    {
        /// <summary>
        /// 发票代码
        /// </summary>
        [JsonProperty("invoiceCode")]
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 发票号码
        /// </summary>
        [JsonProperty("invoiceNumber")]
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        [JsonProperty("issueDate")]
        public string IssueDate { get; set; }

        /// <summary>
        /// 校验码
        /// </summary>
        [JsonProperty("checkCode")]
        public string CheckCode { get; set; }

        /// <summary>
        /// PDF下载地址
        /// </summary>
        [JsonProperty("pdfUrl")]
        public string PdfUrl { get; set; }
    }

    /// <summary>
    /// 发票查询响应类
    /// </summary>
    public class InvoiceQueryResponse
    {
        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("data")]
        public InvoiceQueryData Data { get; set; }
    }

    /// <summary>
    /// 发票查询数据
    /// </summary>
    public class InvoiceQueryData
    {
        /// <summary>
        /// 交易流水号
        /// </summary>
        [JsonProperty("serialNumber")]
        public string SerialNumber { get; set; }

        /// <summary>
        /// 发票状态：0 开票中，1 已开票，2 开票失败
        /// </summary>
        [JsonProperty("status")]
        public int Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        [JsonProperty("statusDesc")]
        public string StatusDesc { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        [JsonProperty("invoiceCode")]
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 发票号码
        /// </summary>
        [JsonProperty("invoiceNumber")]
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// PDF下载地址
        /// </summary>
        [JsonProperty("pdfUrl")]
        public string PdfUrl { get; set; }
    }

    // 定义数据模型
    public class RootObject
    {
        public string Method { get; set; }
        public string RequestId { get; set; }
        public Response Response { get; set; }
        public bool Success { get; set; }
    }

    public class Response
    {
        public Success[] Success { get; set; }
    }

    public class Success
    {
        public string EInvoiceUrl { get; set; }
        public string BuyerEmail { get; set; }
        public InvoiceDetail[] InvoiceDetailList { get; set; }
        public double InvoiceTotalTax { get; set; }
        public string InvoiceDate { get; set; }
        public string InvoiceCode { get; set; }
        public double InvoiceTotalPriceTax { get; set; }
        public string SerialNo { get; set; }
        public string SellerTaxNo { get; set; }
        public string InvoiceQrCode { get; set; }
        public double InvoiceTotalPrice { get; set; }
        public InvoiceDetail[] InvoiceDetailsList { get; set; }
        public string InvoiceTypeCode { get; set; }
        public string InvoiceNo { get; set; }
        public string MulPurchaserMark { get; set; }
    }

    public class InvoiceDetail
    {
        public object Ext { get; set; }
        public double GoodsTaxRate { get; set; }
        public double GoodsPrice { get; set; }
        public string InvoiceLineNature { get; set; }
        public int GoodsQuantity { get; set; }
        public double GoodsTotalTax { get; set; }
        public string GoodsCode { get; set; }
        public string GoodsName { get; set; }
        public int GoodsLineNo { get; set; }
        public double GoodsTotalPrice { get; set; }
    }
} 