# AspNetCore SSE Server Tools Test
Write-Host "=== AspNetCore SSE Server Tools Test ===" -ForegroundColor Green

$serverUrl = "http://localhost:3001"
$sessionId = "test-session-$(Get-Random)"

# Function to make JSON-RPC requests via Streamable HTTP
function Invoke-JsonRpcRequest {
    param(
        [string]$Method,
        [hashtable]$Params = @{},
        [string]$Id = "1"
    )
    
    $jsonRpcRequest = @{
        jsonrpc = "2.0"
        id = $Id
        method = $Method
        params = $Params
    } | ConvertTo-Json -Depth 5
    
    try {
        $response = Invoke-WebRequest -Uri "$serverUrl" -Method POST -Body $jsonRpcRequest -ContentType "application/json" -TimeoutSec 30
        return $response.Content | ConvertFrom-Json
    }
    catch {
        Write-Host "❌ Request failed: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Test 1: Server Health Check
Write-Host "`n1. Testing server health..." -ForegroundColor Yellow
try {
    # Test the SSE endpoint to check if server is running
    $response = Invoke-WebRequest -Uri "$serverUrl/sse" -Method GET -TimeoutSec 5
    Write-Host "✅ Server is running" -ForegroundColor Green
}
catch {
    Write-Host "❌ Server health check failed. Make sure the server is running on $serverUrl" -ForegroundColor Red
    Write-Host "   Start the server with: dotnet run" -ForegroundColor Yellow
    exit 1
}

# Test 2: Initialize MCP Connection
Write-Host "`n2. Testing MCP initialization..." -ForegroundColor Yellow
$initResponse = Invoke-JsonRpcRequest -Method "initialize" -Params @{
    protocolVersion = "2024-11-05"
    capabilities = @{
        tools = @{}
    }
    clientInfo = @{
        name = "test-client"
        version = "1.0.0"
    }
}

if ($initResponse -and $initResponse.result) {
    Write-Host "✅ MCP initialization successful" -ForegroundColor Green
    Write-Host "   Server: $($initResponse.result.serverInfo.name) v$($initResponse.result.serverInfo.version)" -ForegroundColor Cyan
} else {
    Write-Host "❌ MCP initialization failed" -ForegroundColor Red
}

# Test 3: List Available Tools
Write-Host "`n3. Testing tools/list..." -ForegroundColor Yellow
$toolsResponse = Invoke-JsonRpcRequest -Method "tools/list"

if ($toolsResponse -and $toolsResponse.result -and $toolsResponse.result.tools) {
    Write-Host "✅ Tools list retrieved - Found $($toolsResponse.result.tools.Count) tools" -ForegroundColor Green
    $toolsResponse.result.tools | ForEach-Object {
        Write-Host "   - $($_.name): $($_.description)" -ForegroundColor Cyan
    }
    $availableTools = $toolsResponse.result.tools
} else {
    Write-Host "❌ Failed to retrieve tools list" -ForegroundColor Red
    exit 1
}

# Test 4: Test Calculator Tool
Write-Host "`n4. Testing CalculatorTool..." -ForegroundColor Yellow
$calcResponse = Invoke-JsonRpcRequest -Method "tools/call" -Params @{
    name = "Add"
    arguments = @{
        a = 10
        b = 5
    }
} -Id "calc-test"

if ($calcResponse -and $calcResponse.result) {
    Write-Host "✅ Calculator tool working: 10 + 5 = $($calcResponse.result.content[0].text)" -ForegroundColor Green
} else {
    Write-Host "❌ Calculator tool failed" -ForegroundColor Red
}

# Test 5: Test Echo Tool
Write-Host "`n5. Testing EchoTool..." -ForegroundColor Yellow
$echoResponse = Invoke-JsonRpcRequest -Method "tools/call" -Params @{
    name = "Echo"
    arguments = @{
        message = "Hello MCP Server!"
    }
} -Id "echo-test"

if ($echoResponse -and $echoResponse.result) {
    Write-Host "✅ Echo tool working: $($echoResponse.result.content[0].text)" -ForegroundColor Green
} else {
    Write-Host "❌ Echo tool failed" -ForegroundColor Red
}

# Test 6: Test Product Search (Database Tool)
Write-Host "`n6. Testing ProductTools - SearchProductsAsync..." -ForegroundColor Yellow
$productResponse = Invoke-JsonRpcRequest -Method "tools/call" -Params @{
    name = "SearchProductsAsync"
    arguments = @{
        keyword = ""
        page = 1
        pageSize = 3
    }
} -Id "product-test"

if ($productResponse -and $productResponse.result) {
    Write-Host "✅ Product search tool working" -ForegroundColor Green
    $result = $productResponse.result.content[0].text
    if ($result -like "*Search Results*" -or $result -like "*No products found*" -or $result -like "*Error*") {
        Write-Host "   Result preview: $($result.Substring(0, [Math]::Min(100, $result.Length)))..." -ForegroundColor Cyan
    } else {
        Write-Host "   Result: $result" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Product search tool failed" -ForegroundColor Red
}

# Test 7: Test Order Search (Database Tool)
Write-Host "`n7. Testing OrderTools - SearchOrdersAsync..." -ForegroundColor Yellow
$orderResponse = Invoke-JsonRpcRequest -Method "tools/call" -Params @{
    name = "SearchOrdersAsync"
    arguments = @{
        keyword = ""
        page = 1
        pageSize = 3
    }
} -Id "order-test"

if ($orderResponse -and $orderResponse.result) {
    Write-Host "✅ Order search tool working" -ForegroundColor Green
    $result = $orderResponse.result.content[0].text
    if ($result -like "*Search Results*" -or $result -like "*No orders found*" -or $result -like "*Error*") {
        Write-Host "   Result preview: $($result.Substring(0, [Math]::Min(100, $result.Length)))..." -ForegroundColor Cyan
    } else {
        Write-Host "   Result: $result" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Order search tool failed" -ForegroundColor Red
}

# Test 8: Test Shop Search (Database Tool)
Write-Host "`n8. Testing ShopTools - SearchShopsAsync..." -ForegroundColor Yellow
$shopResponse = Invoke-JsonRpcRequest -Method "tools/call" -Params @{
    name = "SearchShopsAsync"
    arguments = @{
        keyword = ""
        page = 1
        pageSize = 3
    }
} -Id "shop-test"

if ($shopResponse -and $shopResponse.result) {
    Write-Host "✅ Shop search tool working" -ForegroundColor Green
    $result = $shopResponse.result.content[0].text
    if ($result -like "*Search Results*" -or $result -like "*No shops found*" -or $result -like "*Error*") {
        Write-Host "   Result preview: $($result.Substring(0, [Math]::Min(100, $result.Length)))..." -ForegroundColor Cyan
    } else {
        Write-Host "   Result: $result" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Shop search tool failed" -ForegroundColor Red
}

# Test 9: Test User Search (Database Tool)
Write-Host "`n9. Testing UserTools - SearchUsersAsync..." -ForegroundColor Yellow
$userResponse = Invoke-JsonRpcRequest -Method "tools/call" -Params @{
    name = "SearchUsersAsync"
    arguments = @{
        keyword = ""
        page = 1
        pageSize = 3
    }
} -Id "user-test"

if ($userResponse -and $userResponse.result) {
    Write-Host "✅ User search tool working" -ForegroundColor Green
    $result = $userResponse.result.content[0].text
    if ($result -like "*Search Results*" -or $result -like "*No users found*" -or $result -like "*Error*") {
        Write-Host "   Result preview: $($result.Substring(0, [Math]::Min(100, $result.Length)))..." -ForegroundColor Cyan
    } else {
        Write-Host "   Result: $result" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ User search tool failed" -ForegroundColor Red
}

Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "AspNetCore SSE Server with HeartFlower Mall tools is ready!" -ForegroundColor Green
Write-Host "Server URL: $serverUrl" -ForegroundColor Yellow
Write-Host "MCP Endpoint: $serverUrl (POST)" -ForegroundColor Yellow
Write-Host "SSE Endpoint: $serverUrl/sse (GET)" -ForegroundColor Yellow
Write-Host "Available Tools: Calculator, Echo, Product Management, Order Management, Shop Management, User Management" -ForegroundColor Yellow

Write-Host "`nTo start the server:" -ForegroundColor Cyan
Write-Host "  cd AspNetCoreSseServer" -ForegroundColor White
Write-Host "  dotnet run" -ForegroundColor White