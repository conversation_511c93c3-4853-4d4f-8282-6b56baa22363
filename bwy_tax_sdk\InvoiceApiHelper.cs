using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace BwyTax.Sdk
{
    /// <summary>
    /// 电子发票API辅助类
    /// </summary>
    internal class InvoiceApiHelper
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _accessKey;
        private readonly string _secretKey;
        private readonly ILogger _logger;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="baseUrl">API基础URL</param>
        /// <param name="accessKey">访问密钥</param>
        /// <param name="secretKey">秘密密钥</param>
        /// <param name="logger">日志记录器</param>
        public InvoiceApiHelper(string baseUrl, string accessKey, string secretKey, ILogger logger)
        {
            _logger = logger;
            _httpClient = HttpClientFactory.CreateLoggingClient(_logger);
            _baseUrl = baseUrl.TrimEnd('/');
            _accessKey = accessKey;
            _secretKey = secretKey;
        }

        /// <summary>
        /// 获取访问令牌
        /// </summary>
        /// <returns>令牌信息</returns>
        public async Task<TokenResponse> GetTokenAsync()
        {
            try
            {
                var tokenUrl = $"{_baseUrl}/open/api/token";
                
                var requestData = new
                {
                    accessKey = _accessKey,
                    secretKey = _secretKey
                };
                
                var requestJson = JsonConvert.SerializeObject(requestData);
                
                var content = new StringContent(requestJson, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync(tokenUrl, content);
                response.EnsureSuccessStatusCode();
                
                var jsonResult = await response.Content.ReadAsStringAsync();
                
                return JsonConvert.DeserializeObject<TokenResponse>(jsonResult);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetToken", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 开具电子发票（2.0版本接口）
        /// </summary>
        /// <param name="invoiceRequest">发票请求数据</param>
        /// <param name="token">访问令牌</param>
        /// <param name="isTest">是否使用税控测试环境</param>
        /// <returns>发票开具结果</returns>
        public async Task<InvoiceResultV3> IssueInvoiceV2Async(InvoiceRequestV2 invoiceRequest, string token, bool isTest = false)
        {
            try
            {
                // 根据是否测试环境选择URL
                var invoiceUrl = isTest
                    ? $"{_baseUrl}/open/api/sk/test/invoice"   // 税控测试
                    : $"{_baseUrl}/open/api/invoice";          // 生产环境
                
                // 设置请求头
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("token", token);
                
                var requestJson = JsonConvert.SerializeObject(invoiceRequest);
                
                var content = new StringContent(requestJson, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync(invoiceUrl, content);
                response.EnsureSuccessStatusCode();
                
                var jsonResult = await response.Content.ReadAsStringAsync();
                
                return JsonConvert.DeserializeObject<InvoiceResultV3>(jsonResult);
                //return JsonConvert.DeserializeObject<InvoiceResponseV2>(jsonResult);
            }
            catch (Exception ex)
            {
                _logger.LogError("IssueInvoiceV2", ex.Message);
                throw;
            }
        }

        public async Task<InvoiceCreateResponseData> createInvoiceV2Async(InvoiceCreateDto invoiceRequest, string token, bool isTest = false)
        {
            try
            {
                // 根据是否测试环境选择URL
                var invoiceUrl = isTest
                    ? $"{_baseUrl}/open/api/sk/test/create"   // 税控测试
                    : $"{_baseUrl}/open/api/create";          // 生产环境

                // 设置请求头
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("token", token);

                var requestJson = JsonConvert.SerializeObject(invoiceRequest);

                var content = new StringContent(requestJson, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(invoiceUrl, content);
                response.EnsureSuccessStatusCode();

                var jsonResult = await response.Content.ReadAsStringAsync();

                return JsonConvert.DeserializeObject<InvoiceCreateResponseData>(jsonResult);
                //return JsonConvert.DeserializeObject<InvoiceResponseV2>(jsonResult);
            }
            catch (Exception ex)
            {
                _logger.LogError("IssueInvoiceV2", ex.Message);
                throw;
            }
        }
    }
} 