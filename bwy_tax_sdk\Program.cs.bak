using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace bwy_tax_sdk
{
    class Program
    {
        // API服务地址
        private const string ApiBaseUrl = "http://baishanyunpiao.com/prod-api";
        // 应用ID
        private const string AccessKey = "weW3GZc6PTqYHPTZ5aMtx2HIeMM4Nq";
        // 应用密钥
        private const string SecretKey = "nSWnsegGhQFXb4K7HSPs9oxA6RQ5X1";
        // 销方税号
        private const string SellerTaxNo = "91120106MAE2H6CU5U";
        // 开票终端代码
        private const string TerminalCode = "";

        static async Task Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("电子发票API测试程序启动...");

            try
            {
                // 创建日志记录器
                var logger = new ConsoleLogger();
                
                // 创建API帮助类实例
                var apiHelper = new InvoiceApiHelper(ApiBaseUrl, AccessKey, SecretKey, logger);

                // 测试获取Token
                Console.WriteLine("\n===== 开始测试获取Token =====");
                var tokenResponse = await apiHelper.GetTokenAsync();
                
                if (tokenResponse.State)
                {
                    Console.WriteLine($"获取Token成功: {tokenResponse.Data}");
                    string token = tokenResponse.Data;

                    // 测试开具发票（2.0版本）
                    await TestInvoiceV2Async(apiHelper, token);
                }
                else
                {
                    Console.WriteLine($"获取Token失败: {tokenResponse.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生异常: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
            }

            Console.WriteLine("\n===== 测试完成 =====");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
        
        /// <summary>
        /// 测试开票接口（2.0版本）
        /// </summary>
        private static async Task TestInvoiceV2Async(InvoiceApiHelper apiHelper, string token)
        {
            // 测试开具发票（2.0版本）
            Console.WriteLine("\n===== 开始测试开具发票 =====");
            var invoiceRequest = CreateTestInvoiceRequestV2();
            
            // 询问是否使用测试环境
            Console.Write("是否使用税控测试环境？(y/n): ");
            var useTestEnv = Console.ReadLine()?.ToLower() == "y";
            
            var invoiceResponse = await apiHelper.IssueInvoiceV2Async(invoiceRequest, token, useTestEnv);

            if (invoiceResponse.Success != null && invoiceResponse.Success.Count > 0)
            {
                Console.WriteLine("开具发票成功！");
                
                foreach (var result in invoiceResponse.Success)
                {
                    Console.WriteLine($"发票代码: {result.InvoiceCode}");
                    Console.WriteLine($"发票号码: {result.InvoiceNo}");
                    Console.WriteLine($"开票日期: {result.InvoiceDate}");
                    Console.WriteLine($"电子版式下载地址: {result.EInvoiceUrl}");
                    Console.WriteLine($"合计金额: {result.InvoiceTotalPrice}");
                    Console.WriteLine($"合计税额: {result.InvoiceTotalTax}");
                    Console.WriteLine($"价税合计: {result.InvoiceTotalPriceTax}");
                    Console.WriteLine($"流水号: {result.SerialNo}");
                    
                    if (result.InvoiceDetailsList != null)
                    {
                        Console.WriteLine("明细项：");
                        foreach (var detail in result.InvoiceDetailsList)
                        {
                            Console.WriteLine($"  - 行号: {detail.GoodsLineNo}, 商品名称: {detail.GoodsName}, 税率: {detail.GoodsTaxRate}");
                        }
                    }
                }
            }
            else if (invoiceResponse.Fail != null && invoiceResponse.Fail.Count > 0)
            {
                Console.WriteLine("部分或全部开具失败！失败明细：");
                foreach (var failItem in invoiceResponse.Fail)
                {
                    Console.WriteLine($"商品名称: {failItem.GoodsName}, 金额: {failItem.GoodsTotalPrice}");
                }
            }
            else
            {
                Console.WriteLine("开具发票失败：未收到有效响应");
            }
        }

        /// <summary>
        /// 创建测试用的发票请求数据（2.0版本）
        /// </summary>
        private static InvoiceRequestV2 CreateTestInvoiceRequestV2()
        {
            // 生成唯一流水号
            string serialNumber = DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999);

            // 创建发票请求对象
            var request = new InvoiceRequestV2
            {
                TaxNo = SellerTaxNo,  // 销方税号
                InvoiceTerminalCode = TerminalCode, // 开票终端代码
                IsSplit = false,      // 不启用超限拆分
                IsReturnRedInfo = "1", // 返回蓝票已红冲信息
                FormatGenerate = true, // 生成版式并返回链接
                Data = new InvoiceDataV2
                {
                    InvoiceTypeCode = "02",    // 数电普票传值
                    InvoiceType = "0",          // 蓝票
                    SerialNo = serialNumber,    // 流水号
                    SellerBankName = "招商银行股份有限公司天津鼓楼支行",
                    SellerBankNumber = "***************",
                    BuyerName = "武亦杰",
                    //BuyerTaxNo = "91120106MAE2H6CU5U",
                    //BuyerAddressPhone = "",
                    //BuyerBankAccount = "",
                    PriceTaxMark = 1, // 含税1
                    //InvoiceTotalPrice = 10,
                    //InvoiceTotalTax = 10.09m,
                    //InvoiceTotalPriceTax = 10.83m,
                    Drawer = "刘涛",          // 开票人
                    Checker = "刘涛",         // 复核人
                    Payee = "刘涛",           // 收款人
                    InvoiceDetailsList = new List<InvoiceDetailV2>
                    {
                        new InvoiceDetailV2
                        {
                            GoodsLineNo = 1,
                            GoodsCode = "1010101070000000000", // 税收分类编码
                            GoodsName = "燕麦",
                            GoodsQuantity = 1,
                            GoodsPrice = 10,
                            GoodsTotalPrice = 10m,
                            //GoodsTotalPriceTax = 10m,
                            GoodsTaxRate = 0.09m,
                            GoodsTotalTax = 0.83m,
                            InvoiceLineNature = "0" // 正常
                        }
                    }
                }
            };

            return request;
        }
    }
} 