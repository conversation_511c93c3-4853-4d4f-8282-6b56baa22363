using System.ComponentModel;
using System.Text;
using Microsoft.EntityFrameworkCore;
using ModelContextProtocol.Server;
using TestServerWithHosting.DataAccess;
using TestServerWithHosting.DataAccess.Entities;

namespace TestServerWithHosting.Tools;

[McpServerToolType]
public class UserTools
{
    private readonly IRepository<User, long> _userRepository;

    public UserTools(IRepository<User, long> userRepository)
    {
        _userRepository = userRepository;
    }

    [McpServerTool, Description("搜索用户信息，支持按用户名、姓名、邮箱或手机号搜索")]
    public async Task<string> SearchUsersAsync(
        [Description("搜索关键词，可以是用户名、姓名、邮箱或手机号，为空则返回所有用户")] string keyword = "",
        [Description("页码，从1开始")] int page = 1,
        [Description("每页数量")] int pageSize = 10)
    {
        try
        {
            var query = _userRepository.GetAll().Where(u => !u.IsDeleted);

            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(u => (u.UserName != null && u.UserName.Contains(keyword)) || 
                                       (u.Name != null && u.Name.Contains(keyword)) || 
                                       (u.EmailAddress != null && u.EmailAddress.Contains(keyword)) ||
                                       (u.PhoneNumber != null && u.PhoneNumber.Contains(keyword)));
            }

            var totalCount = await query.CountAsync();
            var users = await query
                .OrderByDescending(u => u.CreationTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            if (!users.Any())
            {
                return $"未找到符合条件的用户。关键词: {keyword}";
            }

            var result = new StringBuilder();
            result.AppendLine($"用户搜索结果 (第{page}页, 共{totalCount}条记录):");
            result.AppendLine($"关键词: {keyword}");
            result.AppendLine();

            foreach (var user in users)
            {
                var statusText = user.IsActive ? "正常" : "禁用";
                var emailStatus = user.IsEmailConfirmed ? "已验证" : "未验证";
                var phoneStatus = user.IsPhoneNumberConfirmed ? "已验证" : "未验证";

                result.AppendLine($"用户ID: {user.Id}");
                result.AppendLine($"用户名: {user.UserName ?? "未设置"}");
                result.AppendLine($"姓名: {user.Name ?? "未设置"}");
                result.AppendLine($"邮箱: {user.EmailAddress ?? "未设置"} ({emailStatus})");
                result.AppendLine($"手机: {user.PhoneNumber ?? "未设置"} ({phoneStatus})");
                result.AppendLine($"状态: {statusText}");
                result.AppendLine($"注册时间: {user.CreationTime:yyyy-MM-dd HH:mm:ss}");
                result.AppendLine("---");
            }

            return result.ToString();
        }
        catch (Exception ex)
        {
            return $"搜索用户时发生错误: {ex.Message}";
        }
    }

    [McpServerTool, Description("根据用户ID获取用户详细信息")]
    public async Task<string> GetUserByIdAsync(
        [Description("用户ID")] string userId)
    {
        try
        {
            if (!long.TryParse(userId, out var id))
            {
                return "错误：无效的用户ID格式";
            }

            var user = await _userRepository.FirstOrDefaultAsync(u => u.Id == id && !u.IsDeleted);
            if (user == null)
            {
                return "未找到指定的用户";
            }

            var statusText = user.IsActive ? "正常" : "禁用";
            var emailStatus = user.IsEmailConfirmed ? "已验证" : "未验证";
            var phoneStatus = user.IsPhoneNumberConfirmed ? "已验证" : "未验证";

            return $"用户详情：\n" +
                   $"用户ID: {user.Id}\n" +
                   $"用户名: {user.UserName ?? "未设置"}\n" +
                   $"姓名: {user.Name ?? "未设置"}\n" +
                   $"邮箱: {user.EmailAddress ?? "未设置"} ({emailStatus})\n" +
                   $"手机: {user.PhoneNumber ?? "未设置"} ({phoneStatus})\n" +
                   $"账户状态: {statusText}\n" +
                   $"注册时间: {user.CreationTime:yyyy-MM-dd HH:mm:ss}";
        }
        catch (Exception ex)
        {
            return $"获取用户信息时发生错误: {ex.Message}";
        }
    }
}