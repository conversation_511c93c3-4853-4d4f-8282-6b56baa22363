@echo off
chcp 65001 >nul
echo 🔨 心花商城 MCP 服务器 - 构建并启动

echo.
echo 🛑 第一步：停止现有服务器...
call stop_server.bat

echo.
echo 🧹 第二步：清理构建输出...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
echo ✅ 清理完成

echo.
echo 🔨 第三步：构建项目...
dotnet build --configuration Release
if %ERRORLEVEL% neq 0 (
    echo ❌ 构建失败！
    pause
    exit /b 1
)
echo ✅ 构建成功

echo.
echo 🚀 第四步：启动服务器...
echo 📡 服务地址: http://localhost:5000
echo 🔧 可用工具: 商品管理、订单查询、用户管理、店铺管理、计算器、回声
echo.
dotnet run --configuration Release --urls "http://localhost:5000"