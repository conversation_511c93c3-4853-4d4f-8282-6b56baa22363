using System;
using System.Threading.Tasks;
using BwyTax.Sdk;

namespace BwyTax.Examples
{
    /// <summary>
    /// 使用SDK的示例代码
    /// </summary>
    public static class Examples
    {
        // 配置信息
        private const string ApiBaseUrl = "http://baishanyunpiao.com/prod-api";
        private const string AccessKey = "您的应用ID";
        private const string SecretKey = "您的应用密钥";
        private const string SellerTaxNo = "销方税号";
        private const string TerminalCode = ""; // 开票终端代码，可选

        /// <summary>
        /// 运行示例
        /// </summary>
        public static async Task RunExampleAsync()
        {
            Console.WriteLine("开始运行百望云电子发票SDK示例...");

            try
            {
                // 创建客户端实例
                var client = new BwyTaxInvoiceClient(
                    apiBaseUrl: ApiBaseUrl,
                    accessKey: AccessKey,
                    secretKey: SecretKey,
                    sellerTaxNo: SellerTaxNo,
                    terminalCode: TerminalCode
                );

                // 示例1：获取Token
                Console.WriteLine("\n===== 示例1：获取Token =====");
                var tokenResponse = await client.GetTokenAsync();
                if (tokenResponse.State)
                {
                    Console.WriteLine($"获取Token成功: {tokenResponse.Data}");
                }
                else
                {
                    Console.WriteLine($"获取Token失败: {tokenResponse.Message}");
                    return;
                }

                // 示例2：使用快速开票方法
                Console.WriteLine("\n===== 示例2：快速开票 =====");
                
                // 创建商品明细
                var goodsList = new[]
                {
                    new InvoiceItem
                    {
                        GoodsCode = "1010101070000000000", // 税收分类编码
                        GoodsName = "燕麦",
                        GoodsQuantity = 1,
                        GoodsPrice = 10,
                        GoodsTotalPrice = 10m,
                        GoodsTaxRate = 0.09m,
                        GoodsTotalTax = 0.83m
                    }
                };

                // 询问是否使用测试环境
                Console.Write("是否使用税控测试环境？(y/n): ");
                var useTestEnv = Console.ReadLine()?.ToLower() == "y";
                
                // 调用快速开票方法
                var quickResponse = await client.QuickIssueInvoiceAsync(
                    invoiceTypeCode: "02", // 全电普票
                    buyerName: "武亦杰",
                    buyerTaxNo: null,
                    goodsList: goodsList,
                    drawer: "刘涛",
                    checker: "刘涛",
                    payee: "刘涛",
                    sellerBankName: "招商银行股份有限公司天津鼓楼支行",
                    sellerBankNumber: "***************",
                    isTest: useTestEnv
                );
                
                // 处理结果
                if (quickResponse.Success != null && quickResponse.Success.Count > 0)
                {
                    Console.WriteLine("开具发票成功！");
                    
                    foreach (var result in quickResponse.Success)
                    {
                        Console.WriteLine($"发票代码: {result.InvoiceCode}");
                        Console.WriteLine($"发票号码: {result.InvoiceNo}");
                        Console.WriteLine($"开票日期: {result.InvoiceDate}");
                        Console.WriteLine($"电子版式下载地址: {result.EInvoiceUrl}");
                        Console.WriteLine($"合计金额: {result.InvoiceTotalPrice}");
                        Console.WriteLine($"合计税额: {result.InvoiceTotalTax}");
                        Console.WriteLine($"价税合计: {result.InvoiceTotalPriceTax}");
                        Console.WriteLine($"流水号: {result.SerialNo}");
                    }
                }
                else if (quickResponse.Fail != null && quickResponse.Fail.Count > 0)
                {
                    Console.WriteLine("开具发票失败！失败明细：");
                    foreach (var failItem in quickResponse.Fail)
                    {
                        Console.WriteLine($"商品名称: {failItem.GoodsName}, 金额: {failItem.GoodsTotalPrice}");
                    }
                }
                else
                {
                    Console.WriteLine("开具发票失败：未收到有效响应");
                }
                
                // 示例3：高级开票参数设置
                Console.WriteLine("\n===== 示例3：高级开票参数设置 =====");
                Console.WriteLine("查看 Examples.cs 中的代码了解更多高级设置");
                
                /* 
                // 创建发票数据
                var invoiceData = new InvoiceDataV2
                {
                    InvoiceTypeCode = "02",   // 全电普票
                    InvoiceType = "0",        // 蓝票
                    SerialNo = "ORDER123456", // 自定义流水号
                    BuyerName = "购方名称",
                    BuyerTaxNo = "购方税号",
                    BuyerAddressPhone = "购方地址电话",
                    BuyerBankAccount = "购方银行账号",
                    SellerBankName = "销方银行名称",
                    SellerBankNumber = "销方银行账号",
                    PriceTaxMark = 1,         // 含税
                    Remarks = "备注信息",
                    Drawer = "开票人",
                    Checker = "复核人",
                    Payee = "收款人",
                    InvoiceDetailsList = new System.Collections.Generic.List<InvoiceDetailV2>
                    {
                        new InvoiceDetailV2
                        {
                            GoodsLineNo = 1,
                            GoodsCode = "1010101070000000000",
                            GoodsName = "燕麦",
                            GoodsQuantity = 1,
                            GoodsPrice = 10,
                            GoodsTotalPrice = 10m,
                            GoodsTaxRate = 0.09m,
                            GoodsTotalTax = 0.83m,
                            Unit = "袋",
                            SpecificationModel = "500g",
                            InvoiceLineNature = "0"
                        }
                    }
                };
                
                // 调用开票接口
                var response = await client.IssueInvoiceAsync(
                    invoiceData: invoiceData,
                    isSplit: false,
                    isReturnRedInfo: true,
                    formatGenerate: true,
                    isTest: false
                );
                */
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生异常: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
            }

            Console.WriteLine("\n===== 示例结束 =====");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
} 