@echo off
chcp 65001 >nul
echo 🚀 启动心花商城 MCP 服务器...
echo.

REM 检查是否已有服务器在运行
netstat -an | findstr ":5000" >nul
if %ERRORLEVEL% equ 0 (
    echo ⚠️  端口 5000 已被占用，可能有服务器正在运行
    echo 💡 如需重启，请先运行 stop_server.bat
    echo.
    choice /C YN /M "是否继续启动 (Y/N)"
    if errorlevel 2 exit /b 0
)

echo 📡 服务地址: http://localhost:5000
echo 🔧 可用工具: 商品管理、订单查询、用户管理、店铺管理、计算器、回声
echo 📚 测试命令: powershell -ExecutionPolicy Bypass -File quick_test.ps1
echo.
echo ⏳ 正在启动服务器...

dotnet run --urls "http://localhost:5000"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ 服务器启动失败！
    echo 💡 可能的解决方案:
    echo    1. 运行 build_and_start.bat 重新构建
    echo    2. 检查数据库连接是否正常
    echo    3. 确保端口 5000 未被占用
    pause
)