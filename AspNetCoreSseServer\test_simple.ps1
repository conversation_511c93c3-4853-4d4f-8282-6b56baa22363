# 简单测试脚本 - 避免复杂字符
Write-Host "Simple Test for HeartFlower Mall MCP Server" -ForegroundColor Green

$baseUrl = "http://localhost:5000"

# Test server response
Write-Host "Testing server..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri $baseUrl -Method GET -TimeoutSec 5
    Write-Host "Server is running: $($response.name)" -ForegroundColor Green
} catch {
    Write-Host "Server not responding. Please start the server first." -ForegroundColor Red
    Write-Host "Run: start_chinese.bat" -ForegroundColor Yellow
    exit 1
}

# Test tools list
Write-Host "Testing tools list..." -ForegroundColor Yellow
try {
    $tools = Invoke-RestMethod -Uri "$baseUrl/tools/list" -Method GET -TimeoutSec 5
    Write-Host "Found $($tools.tools.Count) tools" -ForegroundColor Green
} catch {
    Write-Host "Failed to get tools list" -ForegroundColor Red
}

# Test echo tool
Write-Host "Testing echo tool..." -ForegroundColor Yellow
try {
    $echo = Invoke-RestMethod -Uri "$baseUrl/tools/call/Echo?message=test" -Method GET -TimeoutSec 5
    Write-Host "Echo result: $($echo.content[0].text)" -ForegroundColor Green
} catch {
    Write-Host "Echo tool failed" -ForegroundColor Red
}

Write-Host "Simple test completed!" -ForegroundColor Green