﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <UserSecretsId>a4e20a70-5009-4b81-b5b6-780b6d43e78e</UserSecretsId>
        <!--
        Anthropic SDK isn't AOT compatible yet
        <PublishAot>true</PublishAot>
        -->
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\src\ModelContextProtocol.Core\ModelContextProtocol.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Anthropic.SDK" />
        <PackageReference Include="Microsoft.Extensions.Hosting" />
        <PackageReference Include="Microsoft.Extensions.AI" />
    </ItemGroup>

</Project>
