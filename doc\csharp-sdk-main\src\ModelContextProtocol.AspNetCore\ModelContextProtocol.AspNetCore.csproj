﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net9.0;net8.0</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <IsPackable>true</IsPackable>
    <PackageId>ModelContextProtocol.AspNetCore</PackageId>
    <Description>ASP.NET Core extensions for the C# Model Context Protocol (MCP) SDK.</Description>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <IsAotCompatible>true</IsAotCompatible>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <None Include="README.md" pack="true" PackagePath="\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ModelContextProtocol\ModelContextProtocol.csproj" />
  </ItemGroup>

</Project>
