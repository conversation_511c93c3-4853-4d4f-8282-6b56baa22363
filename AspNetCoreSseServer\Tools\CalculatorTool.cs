using System.ComponentModel;
using ModelContextProtocol.Server;

namespace TestServerWithHosting.Tools;

[McpServerToolType]
public sealed class CalculatorTool
{
    [McpServerTool, Description("两个数字相加")]
    public static double Add(
        [Description("第一个数字")] double a, 
        [Description("第二个数字")] double b)
    {
        return a + b;
    }

    [McpServerTool, Description("第一个数字减去第二个数字")]
    public static double Subtract(
        [Description("第一个数字")] double a, 
        [Description("第二个数字")] double b)
    {
        return a - b;
    }

    [McpServerTool, Description("两个数字相乘")]
    public static double Multiply(
        [Description("第一个数字")] double a, 
        [Description("第二个数字")] double b)
    {
        return a * b;
    }

    [McpServerTool, Description("第一个数字除以第二个数字")]
    public static double Divide(
        [Description("被除数")] double a, 
        [Description("除数")] double b)
    {
        if (b == 0)
            throw new ArgumentException("不能除以零");
        return a / b;
    }
}