﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.0;</TargetFrameworks>
    <RootNamespace>BwyTax.Sdk</RootNamespace>
    <PackageId>BwyTax.Sdk</PackageId>
    <Version>1.0.0</Version>
    <Authors>BwyTax</Authors>
    <Company>BwyTax</Company>
    <Description>百望云电子发票开具SDK</Description>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>true</GenerateTargetFrameworkAttribute>
    <Copyright>Copyright © 2024</Copyright>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  
  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
      <_Parameter1>BwyTaxSample</_Parameter1>
    </AssemblyAttribute>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
      <_Parameter1>BwyTaxClientSample</_Parameter1>
    </AssemblyAttribute>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
      <_Parameter1>BwyTaxWebApi</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
  
</Project>
