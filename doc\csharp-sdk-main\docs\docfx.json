{"$schema": "https://raw.githubusercontent.com/dotnet/docfx/main/schemas/docfx.schema.json", "metadata": [{"src": [{"src": "../src", "files": ["**/*.c<PERSON><PERSON>j"]}], "output": "api"}], "build": {"content": [{"files": ["**/*.{md,yml}"], "exclude": ["_site/**"]}], "resource": [{"files": ["images/**"]}], "output": "../artifacts/_site", "template": ["default", "modern"], "globalMetadata": {"_appName": "MCP C# SDK", "_appTitle": "MCP C# SDK", "_appLogoPath": "images/mcp.svg", "_appFaviconPath": "images/favicon.ico", "_enableSearch": true, "pdf": false}}}