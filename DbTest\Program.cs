using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using HeartFlower.Mall.EntityFrameworkCore;

class Program
{
    static async Task Main(string[] args)
    {
        try
        {
            Console.WriteLine("=== HeartFlower Mall Database Connectivity Test ===\n");

            // 配置数据库连接
            var connectionString = "Server=47.95.15.9; Database=HeartFlowerMall-Test; User Id=heartflower; Password=*************;";
            Console.WriteLine($"连接字符串: {connectionString.Replace(";Password=*************;", ";Password=***;")}\n");

            var options = new DbContextOptionsBuilder<MallDbContext>()
                .UseSqlServer(connectionString)
                .Options;

            using var dbContext = new MallDbContext(options);

            Console.WriteLine("⏳ 测试数据库连接...");
            
            // 测试数据库连接
            await dbContext.Database.OpenConnectionAsync();
            Console.WriteLine("✅ 数据库连接成功！");
            
            // 测试查询产品表
            Console.WriteLine("⏳ 查询产品表...");
            var productCount = await dbContext.Product.CountAsync();
            Console.WriteLine($"✅ 产品表查询成功，共有 {productCount} 个商品");

            // 测试查询用户表
            Console.WriteLine("⏳ 查询用户表...");
            var userCount = await dbContext.Users.CountAsync();
            Console.WriteLine($"✅ 用户表查询成功，共有 {userCount} 个用户");

            // 测试查询订单表
            Console.WriteLine("⏳ 查询订单表...");
            var orderCount = await dbContext.Order.CountAsync();
            Console.WriteLine($"✅ 订单表查询成功，共有 {orderCount} 个订单");

            // 测试查询店铺表
            Console.WriteLine("⏳ 查询店铺表...");
            var shopCount = await dbContext.ShopList.CountAsync();
            Console.WriteLine($"✅ 店铺表查询成功，共有 {shopCount} 个店铺");

            // 测试查询一个具体的商品
            Console.WriteLine("⏳ 查询商品详情...");
            var firstProduct = await dbContext.Product.FirstOrDefaultAsync();
            if (firstProduct != null)
            {
                Console.WriteLine($"✅ 查询到商品: {firstProduct.ProductName} (ID: {firstProduct.Id})");
            }
            else
            {
                Console.WriteLine("⚠️ 数据库中没有商品数据");
            }

            Console.WriteLine("\n🎉 数据库测试全部成功！");
            Console.WriteLine("🔍 这说明 MCP Server 的数据库配置是正确的，应该能够正常访问数据库。");
            Console.WriteLine("💡 MCP Server 工具返回 'An error occurred invoking...' 可能是由其他原因导致的。");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 数据库测试失败: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"内部异常: {ex.InnerException.Message}");
            }
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }
}