# 快速测试心花商城 MCP 服务器
$baseUrl = "http://localhost:5000"

Write-Host "测试心花商城 MCP 服务器..." -ForegroundColor Green
Write-Host "测试地址: $baseUrl" -ForegroundColor Cyan

# 测试服务器是否响应
Write-Host ""
Write-Host "检查服务器状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri $baseUrl -Method GET -TimeoutSec 5
    Write-Host "服务器正常运行" -ForegroundColor Green
    Write-Host "服务器信息: $($response.name) v$($response.version)" -ForegroundColor Cyan
} catch {
    Write-Host "服务器无响应: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请确保服务器已启动 (运行 start_chinese.bat)" -ForegroundColor Yellow
    exit 1
}

# 快速测试工具列表
Write-Host ""
Write-Host "获取工具列表..." -ForegroundColor Yellow
try {
    $toolsResponse = Invoke-RestMethod -Uri "$baseUrl/tools/list" -Method GET -TimeoutSec 5
    $toolCount = $toolsResponse.tools.Count
    Write-Host "成功获取 $toolCount 个工具" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "可用工具:" -ForegroundColor Cyan
    $toolsResponse.tools | ForEach-Object { 
        Write-Host "  - $($_.name): $($_.description)" -ForegroundColor White
    }
} catch {
    Write-Host "获取工具列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 快速测试回声工具
Write-Host ""
Write-Host "测试回声工具..." -ForegroundColor Yellow
try {
    $echoResponse = Invoke-RestMethod -Uri "$baseUrl/tools/call/Echo?message=测试消息" -Method GET -TimeoutSec 5
    Write-Host "回声工具正常: $($echoResponse.content[0].text)" -ForegroundColor Green
} catch {
    Write-Host "回声工具测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 快速测试计算器
Write-Host ""
Write-Host "测试计算器..." -ForegroundColor Yellow
try {
    $calcResponse = Invoke-RestMethod -Uri "$baseUrl/tools/call/Add?a=5&b=3" -Method GET -TimeoutSec 5
    Write-Host "计算器正常: 5 + 3 = $($calcResponse.content[0].text)" -ForegroundColor Green
} catch {
    Write-Host "计算器测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "快速测试完成!" -ForegroundColor Green
Write-Host "运行 test_tools_chinese.ps1 进行完整测试" -ForegroundColor Yellow