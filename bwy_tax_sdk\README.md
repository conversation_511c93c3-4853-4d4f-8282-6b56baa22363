# BwyTax.Sdk - 百望云电子发票开具SDK

这是一个用于对接百望云电子发票平台的.NET SDK，支持快速集成电子发票开具功能。

## 功能特性

- 支持获取访问令牌
- 支持开具电子发票（V2版本接口）
- 提供便捷的开票方法
- 内置日志记录功能
- 支持多种.NET框架（.NET Standard 2.0, .NET 6.0+）

## 安装

通过NuGet包管理器安装：

```bash
dotnet add package BwyTax.Sdk
```

## 快速开始

### 初始化客户端

   ```csharp
// 创建客户端实例
var client = new BwyTaxInvoiceClient(
    apiBaseUrl: "http://baishanyunpiao.com/prod-api",
    accessKey: "您的应用ID",
    secretKey: "您的应用密钥",
    sellerTaxNo: "销方税号"
);
```

### 开具电子发票

```csharp
// 创建商品明细
var goodsList = new[]
{
    new InvoiceItem
    {
        GoodsCode = "1010101070000000000",  // 税收分类编码
        GoodsName = "燕麦",
        GoodsQuantity = 1,
        GoodsPrice = 10,
        GoodsTotalPrice = 10m,
        GoodsTaxRate = 0.09m,
        GoodsTotalTax = 0.83m
    }
};

// 开具发票
var response = await client.QuickIssueInvoiceAsync(
    invoiceTypeCode: "02",    // 全电普票
    buyerName: "购方名称",
    buyerTaxNo: "购方税号",   // 可选
    goodsList: goodsList,
    drawer: "开票人",
    checker: "复核人",
    payee: "收款人",
    isTest: true             // 是否使用测试环境
);

// 处理结果
if (response.Success != null && response.Success.Count > 0)
{
    // 开票成功
    var result = response.Success[0];
    Console.WriteLine($"发票代码: {result.InvoiceCode}");
    Console.WriteLine($"发票号码: {result.InvoiceNo}");
    Console.WriteLine($"电子版下载地址: {result.EInvoiceUrl}");
}
else if (response.Fail != null && response.Fail.Count > 0)
{
    // 开票失败
    foreach (var failItem in response.Fail)
    {
        Console.WriteLine($"失败: {failItem.GoodsName}, 金额: {failItem.GoodsTotalPrice}");
    }
}
```

### 自定义日志记录器

```csharp
// 实现自定义日志记录器
public class CustomLogger : ILogger
{
    public void LogRequest(string apiName, string url, string requestJson)
    {
        // 自定义请求日志记录
    }
    
    public void LogResponse(string apiName, string url, string responseJson)
    {
        // 自定义响应日志记录
    }
    
    public void LogError(string apiName, string errorMessage)
    {
        // 自定义错误日志记录
    }
}

// 使用自定义日志记录器
var client = new BwyTaxInvoiceClient(
    apiBaseUrl: "http://baishanyunpiao.com/prod-api",
    accessKey: "您的应用ID",
    secretKey: "您的应用密钥",
    sellerTaxNo: "销方税号",
    logger: new CustomLogger()
);
```

## 高级用法

### 高级开票参数设置

```csharp
// 创建发票数据
var invoiceData = new InvoiceDataV2
{
    InvoiceTypeCode = "02",   // 全电普票
    InvoiceType = "0",        // 蓝票
    SerialNo = "ORDER123456", // 自定义流水号
    BuyerName = "购方名称",
    BuyerTaxNo = "购方税号",
    BuyerAddressPhone = "购方地址电话",
    BuyerBankAccount = "购方银行账号",
    SellerBankName = "销方银行名称",
    SellerBankNumber = "销方银行账号",
    PriceTaxMark = 1,         // 含税
    Remarks = "备注信息",
    Drawer = "开票人",
    Checker = "复核人",
    Payee = "收款人",
    InvoiceDetailsList = new List<InvoiceDetailV2>
    {
        new InvoiceDetailV2
        {
            GoodsLineNo = 1,
            GoodsCode = "1010101070000000000",
            GoodsName = "燕麦",
            GoodsQuantity = 1,
            GoodsPrice = 10,
            GoodsTotalPrice = 10m,
            GoodsTaxRate = 0.09m,
            GoodsTotalTax = 0.83m,
            Unit = "袋",
            SpecificationModel = "500g",
            InvoiceLineNature = "0"
        }
    }
};

// 调用开票接口
var response = await client.IssueInvoiceAsync(
    invoiceData: invoiceData,
    isSplit: false,
    isReturnRedInfo: true,
    formatGenerate: true,
    isTest: false
);
```

## 支持

如有任何问题或建议，请提交Issue或联系百望云技术支持。 