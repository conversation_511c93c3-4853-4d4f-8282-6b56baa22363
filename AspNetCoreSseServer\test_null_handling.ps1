# 测试空值处理
Write-Host "测试数据库空值处理..." -ForegroundColor Green

$baseUrl = "http://localhost:5000"

# 测试服务器是否运行
Write-Host "检查服务器状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri $baseUrl -Method GET -TimeoutSec 5
    Write-Host "服务器正常运行" -ForegroundColor Green
} catch {
    Write-Host "服务器未运行，请先启动服务器" -ForegroundColor Red
    exit 1
}

# 测试商品搜索（可能包含空值）
Write-Host ""
Write-Host "测试商品搜索（空值处理）..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/SearchProductsAsync?keyword=&page=1&pageSize=3" -Method GET -TimeoutSec 10
    Write-Host "商品搜索成功，结果:" -ForegroundColor Green
    Write-Host $response.content[0].text
} catch {
    Write-Host "商品搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试订单搜索
Write-Host ""
Write-Host "测试订单搜索（空值处理）..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/SearchOrdersAsync?keyword=&page=1&pageSize=3" -Method GET -TimeoutSec 10
    Write-Host "订单搜索成功，结果:" -ForegroundColor Green
    Write-Host $response.content[0].text
} catch {
    Write-Host "订单搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试店铺搜索
Write-Host ""
Write-Host "测试店铺搜索（空值处理）..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/SearchShopsAsync?keyword=&page=1&pageSize=3" -Method GET -TimeoutSec 10
    Write-Host "店铺搜索成功，结果:" -ForegroundColor Green
    Write-Host $response.content[0].text
} catch {
    Write-Host "店铺搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试用户搜索
Write-Host ""
Write-Host "测试用户搜索（空值处理）..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/SearchUsersAsync?keyword=&page=1&pageSize=3" -Method GET -TimeoutSec 10
    Write-Host "用户搜索成功，结果:" -ForegroundColor Green
    Write-Host $response.content[0].text
} catch {
    Write-Host "用户搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "空值处理测试完成!" -ForegroundColor Green