using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace BwyTax.Sdk
{
    /// <summary>
    /// HTTP请求日志记录处理程序
    /// </summary>
    internal class LoggingHandler : DelegatingHandler
    {
        private readonly ILogger _logger;
        private int _requestId = 0;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="innerHandler">内部处理程序</param>
        /// <param name="logger">日志记录器</param>
        public LoggingHandler(HttpMessageHandler innerHandler, ILogger logger)
            : base(innerHandler)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 发送HTTP请求
        /// </summary>
        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var id = Interlocked.Increment(ref _requestId);
            var requestInfo = await FormatRequest(request, id);
            
            _logger.LogRequest($"HTTP请求 #{id}", request.RequestUri.ToString(), requestInfo);
            
            HttpResponseMessage response = null;
            var startTime = DateTime.Now;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                response = await base.SendAsync(request, cancellationToken);
                
                stopwatch.Stop();
                var responseInfo = await FormatResponse(response, id, stopwatch.ElapsedMilliseconds);
                _logger.LogResponse($"HTTP响应 #{id}", request.RequestUri.ToString(), responseInfo);
                
                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError($"HTTP请求 #{id}", $"发生异常 ({stopwatch.ElapsedMilliseconds}ms): {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 格式化HTTP请求信息
        /// </summary>
        private async Task<string> FormatRequest(HttpRequestMessage request, int id)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine($">>> HTTP请求 #{id} - {request.Method} {request.RequestUri}");
            
            // 添加请求头
            if (request.Headers.Any())
            {
                sb.AppendLine("请求头:");
                foreach (var header in request.Headers)
                {
                    // 隐藏敏感信息
                    if (header.Key.Equals("Authorization", StringComparison.OrdinalIgnoreCase))
                    {
                        sb.AppendLine($"  {header.Key}: Bearer ******");
                    }
                    else
                    {
                        sb.AppendLine($"  {header.Key}: {string.Join(", ", header.Value)}");
                    }
                }
            }
            
            // 添加请求内容
            if (request.Content != null)
            {
                var content = await request.Content.ReadAsStringAsync();
                
                if (!string.IsNullOrEmpty(content))
                {
                    sb.AppendLine("请求内容:");
                    // 尝试格式化JSON
                    sb.AppendLine(FormatJsonIfPossible(content));
                }
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// 格式化HTTP响应信息
        /// </summary>
        private async Task<string> FormatResponse(HttpResponseMessage response, int id, long elapsedMs)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine($"<<< HTTP响应 #{id} - 状态码: {(int)response.StatusCode} {response.StatusCode} (耗时: {elapsedMs}ms)");
            
            // 添加响应头
            if (response.Headers.Any())
            {
                sb.AppendLine("响应头:");
                foreach (var header in response.Headers)
                {
                    sb.AppendLine($"  {header.Key}: {string.Join(", ", header.Value)}");
                }
            }
            
            // 添加响应内容
            if (response.Content != null)
            {
                var content = await response.Content.ReadAsStringAsync();
                
                if (!string.IsNullOrEmpty(content))
                {
                    sb.AppendLine("响应内容:");
                    // 尝试格式化JSON
                    sb.AppendLine(FormatJsonIfPossible(content));
                }
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// 尝试格式化JSON字符串
        /// </summary>
        private string FormatJsonIfPossible(string content)
        {
            try
            {
                if (content.StartsWith("{") || content.StartsWith("["))
                {
                    // 尝试解析和格式化JSON
                    var parsedJson = JsonConvert.DeserializeObject(content);
                    return JsonConvert.SerializeObject(parsedJson, Formatting.Indented);
                }
            }
            catch
            {
                // 解析失败，忽略异常
            }
            
            return content;
        }
    }
    
    /// <summary>
    /// HTTP客户端工厂
    /// </summary>
    internal static class HttpClientFactory
    {
        /// <summary>
        /// 创建带日志记录功能的HTTP客户端
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <returns>HTTP客户端</returns>
        public static HttpClient CreateLoggingClient(ILogger logger)
        {
            var innerHandler = new HttpClientHandler();
            var loggingHandler = new LoggingHandler(innerHandler, logger);
            return new HttpClient(loggingHandler);
        }
    }
} 