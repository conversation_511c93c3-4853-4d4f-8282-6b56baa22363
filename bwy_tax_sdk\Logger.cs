using System;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace BwyTax.Sdk
{
    /// <summary>
    /// 日志记录接口
    /// </summary>
    public interface ILogger
    {
        /// <summary>
        /// 记录请求日志
        /// </summary>
        /// <param name="apiName">API名称</param>
        /// <param name="url">请求URL</param>
        /// <param name="requestJson">请求JSON</param>
        void LogRequest(string apiName, string url, string requestJson);
        
        /// <summary>
        /// 记录响应日志
        /// </summary>
        /// <param name="apiName">API名称</param>
        /// <param name="url">请求URL</param>
        /// <param name="responseJson">响应JSON</param>
        void LogResponse(string apiName, string url, string responseJson);
        
        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="apiName">API名称</param>
        /// <param name="errorMessage">错误信息</param>
        void LogError(string apiName, string errorMessage);
    }
    
    /// <summary>
    /// 控制台日志记录器
    /// </summary>
    public class ConsoleLogger : ILogger
    {
        private const string TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff";
        
        /// <summary>
        /// 获取当前时间戳
        /// </summary>
        private string GetTimestamp()
        {
            return DateTime.Now.ToString(TimestampFormat);
        }
        
        /// <summary>
        /// 美化JSON字符串
        /// </summary>
        private string FormatJson(string json)
        {
            try
            {
                // 尝试解析JSON并格式化
                var parsedJson = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(parsedJson, Formatting.Indented);
            }
            catch
            {
                // 如果解析失败，返回原始字符串
                return json;
            }
        }
        
        /// <summary>
        /// 隐藏敏感信息
        /// </summary>
        private string MaskSensitiveInfo(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json)) return json;
                
                // 解析JSON
                var jObj = JObject.Parse(json);
                
                // 隐藏密钥信息
                if (jObj.ContainsKey("secretKey"))
                {
                    jObj["secretKey"] = "******";
                }
                
                // 隐藏令牌信息
                if (jObj.ContainsKey("access_token"))
                {
                    jObj["access_token"] = "******";
                }
                
                return jObj.ToString();
            }
            catch
            {
                // 如果解析失败，返回原始字符串
                return json;
            }
        }
        
        /// <summary>
        /// 记录请求日志
        /// </summary>
        public void LogRequest(string apiName, string url, string requestJson)
        {
            var maskedJson = MaskSensitiveInfo(requestJson);
            var formattedJson = FormatJson(maskedJson);
            
            var sb = new StringBuilder();
            sb.AppendLine($"[{GetTimestamp()}] === {apiName} 请求开始 ===");
            sb.AppendLine($"URL: {url}");
            sb.AppendLine("请求参数:");
            sb.AppendLine(formattedJson);
            sb.AppendLine();
            
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine(sb.ToString());
            Console.ResetColor();
        }
        
        /// <summary>
        /// 记录响应日志
        /// </summary>
        public void LogResponse(string apiName, string url, string responseJson)
        {
            var formattedJson = FormatJson(responseJson);
            
            var sb = new StringBuilder();
            sb.AppendLine($"[{GetTimestamp()}] === {apiName} 响应结果 ===");
            sb.AppendLine($"URL: {url}");
            sb.AppendLine("响应内容:");
            sb.AppendLine(formattedJson);
            sb.AppendLine();
            
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine(sb.ToString());
            Console.ResetColor();
        }
        
        /// <summary>
        /// 记录错误日志
        /// </summary>
        public void LogError(string apiName, string errorMessage)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"[{GetTimestamp()}] === {apiName} 发生错误 ===");
            sb.AppendLine($"错误信息: {errorMessage}");
            sb.AppendLine();
            
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine(sb.ToString());
            Console.ResetColor();
        }
    }
} 