# 心花商城 MCP 服务器诊断工具
Write-Host "心花商城 MCP 服务器诊断工具" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Gray

# 1. 检查 .NET 环境
Write-Host ""
Write-Host "检查 .NET 环境..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host ".NET 版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host ".NET 未安装或不在 PATH 中" -ForegroundColor Red
}

# 2. 检查项目文件
Write-Host ""
Write-Host "检查项目文件..." -ForegroundColor Yellow
$requiredFiles = @(
    "AspNetCoreSseServer.csproj",
    "Program.cs",
    "appsettings.json"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "$file 存在" -ForegroundColor Green
    } else {
        Write-Host "$file 缺失" -ForegroundColor Red
    }
}

# 3. 检查工具文件
Write-Host ""
Write-Host "检查工具文件..." -ForegroundColor Yellow
$toolFiles = Get-ChildItem -Path "Tools" -Filter "*.cs" -ErrorAction SilentlyContinue
if ($toolFiles) {
    Write-Host "找到 $($toolFiles.Count) 个工具文件:" -ForegroundColor Green
    $toolFiles | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }
} else {
    Write-Host "未找到工具文件" -ForegroundColor Red
}

# 4. 检查端口占用
Write-Host ""
Write-Host "检查端口 5000 状态..." -ForegroundColor Yellow
$portInUse = Get-NetTCPConnection -LocalPort 5000 -ErrorAction SilentlyContinue
if ($portInUse) {
    Write-Host "端口 5000 已被占用" -ForegroundColor Yellow
    $process = Get-Process -Id $portInUse.OwningProcess -ErrorAction SilentlyContinue
    if ($process) {
        Write-Host "  占用进程: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Cyan
    }
} else {
    Write-Host "端口 5000 可用" -ForegroundColor Green
}# 5
. 检查数据库连接配置
Write-Host ""
Write-Host "检查数据库配置..." -ForegroundColor Yellow
try {
    $config = Get-Content -Path "appsettings.json" | ConvertFrom-Json
    if ($config.ConnectionStrings.DefaultConnection) {
        Write-Host "数据库连接字符串已配置" -ForegroundColor Green
        $connStr = $config.ConnectionStrings.DefaultConnection
        if ($connStr -match "Server=([^;]+)") {
            Write-Host "  服务器: $($matches[1])" -ForegroundColor Cyan
        }
        if ($connStr -match "Database=([^;]+)") {
            Write-Host "  数据库: $($matches[1])" -ForegroundColor Cyan
        }
    } else {
        Write-Host "数据库连接字符串未配置" -ForegroundColor Red
    }
} catch {
    Write-Host "无法读取配置文件: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 尝试构建项目
Write-Host ""
Write-Host "尝试构建项目..." -ForegroundColor Yellow
try {
    $buildResult = dotnet build --verbosity quiet --nologo 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "项目构建成功" -ForegroundColor Green
    } else {
        Write-Host "项目构建失败" -ForegroundColor Red
        Write-Host "构建输出:" -ForegroundColor Gray
        Write-Host $buildResult -ForegroundColor Gray
    }
} catch {
    Write-Host "构建过程出错: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 检查脚本文件
Write-Host ""
Write-Host "检查脚本文件..." -ForegroundColor Yellow
$scriptFiles = @(
    "start_chinese.bat",
    "stop_server.bat",
    "quick_test.ps1",
    "test_tools_chinese.ps1"
)

foreach ($script in $scriptFiles) {
    if (Test-Path $script) {
        Write-Host "$script 存在" -ForegroundColor Green
    } else {
        Write-Host "$script 缺失" -ForegroundColor Red
    }
}# 8. 生成诊
断报告
Write-Host ""
Write-Host "生成诊断报告..." -ForegroundColor Yellow
$report = @"
心花商城 MCP 服务器诊断报告
生成时间: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

系统信息:
- 操作系统: $($env:OS)
- PowerShell 版本: $($PSVersionTable.PSVersion)
- .NET 版本: $(try { dotnet --version } catch { "未安装" })

项目状态:
- 工作目录: $(Get-Location)
- 项目文件: $(if (Test-Path "AspNetCoreSseServer.csproj") { "存在" } else { "缺失" })
- 端口 5000: $(if (Get-NetTCPConnection -LocalPort 5000 -ErrorAction SilentlyContinue) { "被占用" } else { "可用" })

建议操作:
1. 确保 .NET 9.0 已安装
2. 运行 build_and_start.bat 重新构建并启动
3. 如果端口被占用，运行 stop_server.bat
4. 检查数据库连接是否正常
"@

$report | Out-File -FilePath "diagnostic_report.txt" -Encoding UTF8
Write-Host "诊断报告已保存到 diagnostic_report.txt" -ForegroundColor Green

Write-Host ""
Write-Host "诊断完成!" -ForegroundColor Green
Write-Host "如果发现问题，请参考诊断报告和使用指南.md" -ForegroundColor Yellow