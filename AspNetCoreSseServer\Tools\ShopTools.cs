using System.ComponentModel;
using System.Text;
using Microsoft.EntityFrameworkCore;
using ModelContextProtocol.Server;
using TestServerWithHosting.DataAccess;
using TestServerWithHosting.DataAccess.Entities;

namespace TestServerWithHosting.Tools;

[McpServerToolType]
public class ShopTools
{
    private readonly IRepository<ShopList, Guid> _shopListRepository;

    public ShopTools(IRepository<ShopList, Guid> shopListRepository)
    {
        _shopListRepository = shopListRepository;
    }

    [McpServerTool, Description("搜索店铺信息，支持按店铺名称、联系人或电话搜索")]
    public async Task<string> SearchShopsAsync(
        [Description("搜索关键词，可以是店铺名称、联系人或电话，为空则返回所有店铺")] string keyword = "",
        [Description("页码，从1开始")] int page = 1,
        [Description("每页数量")] int pageSize = 10)
    {
        try
        {
            var query = _shopListRepository.GetAll().Where(s => !s.IsDeleted);

            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(s => (s.ShopName != null && s.ShopName.Contains(keyword)) || 
                                       (s.Linkman != null && s.Linkman.Contains(keyword)) ||
                                       (s.PhoneNumber != null && s.PhoneNumber.Contains(keyword)));
            }

            var totalCount = await query.CountAsync();
            var shops = await query
                .OrderByDescending(s => s.CreationTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            if (!shops.Any())
            {
                return $"未找到符合条件的店铺。关键词: {keyword}";
            }

            var result = new StringBuilder();
            result.AppendLine($"店铺搜索结果 (第{page}页, 共{totalCount}条记录):");
            result.AppendLine($"关键词: {keyword}");
            result.AppendLine();

            foreach (var shop in shops)
            {
                result.AppendLine($"店铺ID: {shop.Id}");
                result.AppendLine($"店铺名称: {shop.ShopName ?? "未设置"}");
                result.AppendLine($"联系人: {shop.Linkman ?? "未设置"}");
                result.AppendLine($"联系电话: {shop.PhoneNumber ?? "未设置"}");
                result.AppendLine($"备注: {shop.Remarks ?? "无"}");
                result.AppendLine($"创建时间: {shop.CreationTime:yyyy-MM-dd HH:mm:ss}");
                result.AppendLine("---");
            }

            return result.ToString();
        }
        catch (Exception ex)
        {
            return $"搜索店铺时发生错误: {ex.Message}";
        }
    }

    [McpServerTool, Description("根据店铺ID获取店铺详细信息")]
    public async Task<string> GetShopByIdAsync(
        [Description("店铺ID")] string shopId)
    {
        try
        {
            if (!Guid.TryParse(shopId, out var id))
            {
                return "错误：无效的店铺ID格式";
            }

            var shop = await _shopListRepository.FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
            if (shop == null)
            {
                return "未找到指定的店铺";
            }

            return $"店铺详情：\n" +
                   $"店铺ID: {shop.Id}\n" +
                   $"店铺名称: {shop.ShopName ?? "未设置"}\n" +
                   $"联系人: {shop.Linkman ?? "未设置"}\n" +
                   $"联系电话: {shop.PhoneNumber ?? "未设置"}\n" +
                   $"备注: {shop.Remarks ?? "无"}\n" +
                   $"创建时间: {shop.CreationTime:yyyy-MM-dd HH:mm:ss}";
        }
        catch (Exception ex)
        {
            return $"获取店铺信息时发生错误: {ex.Message}";
        }
    }
}