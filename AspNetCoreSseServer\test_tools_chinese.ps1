# 测试中文工具功能
$baseUrl = "http://localhost:5000"

Write-Host "开始测试心花商城 MCP 工具..." -ForegroundColor Green

# 测试工具列表
Write-Host ""
Write-Host "测试工具列表..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/list" -Method GET
    Write-Host "工具列表获取成功" -ForegroundColor Green
    $response.tools | ForEach-Object { Write-Host "  - $($_.name): $($_.description)" }
} catch {
    Write-Host "工具列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试回声工具
Write-Host ""
Write-Host "测试回声工具..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/Echo?message=你好世界" -Method GET
    Write-Host "回声工具测试成功: $($response.content[0].text)" -ForegroundColor Green
} catch {
    Write-Host "回声工具测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试计算器工具
Write-Host ""
Write-Host "测试计算器工具..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/Add?a=10&b=20" -Method GET
    Write-Host "加法计算成功: $($response.content[0].text)" -ForegroundColor Green
} catch {
    Write-Host "加法计算失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试商品搜索
Write-Host ""
Write-Host "测试商品搜索..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/SearchProductsAsync?keyword=&page=1&pageSize=5" -Method GET
    Write-Host "商品搜索成功:" -ForegroundColor Green
    Write-Host $response.content[0].text
} catch {
    Write-Host "商品搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试订单搜索
Write-Host ""
Write-Host "测试订单搜索..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/SearchOrdersAsync?keyword=&page=1&pageSize=5" -Method GET
    Write-Host "订单搜索成功:" -ForegroundColor Green
    Write-Host $response.content[0].text
} catch {
    Write-Host "订单搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试店铺搜索
Write-Host ""
Write-Host "测试店铺搜索..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/SearchShopsAsync?keyword=&page=1&pageSize=5" -Method GET
    Write-Host "店铺搜索成功:" -ForegroundColor Green
    Write-Host $response.content[0].text
} catch {
    Write-Host "店铺搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试用户搜索
Write-Host ""
Write-Host "测试用户搜索..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/tools/call/SearchUsersAsync?keyword=&page=1&pageSize=5" -Method GET
    Write-Host "用户搜索成功:" -ForegroundColor Green
    Write-Host $response.content[0].text
} catch {
    Write-Host "用户搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "测试完成!" -ForegroundColor Green