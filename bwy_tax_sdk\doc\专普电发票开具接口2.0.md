以下 Markdown 已将《专普电发票开具接口 2.0》核心内容结构化，便于在代码里直接对照字段、类型和业务规则实现或校验。  

---

## 1 接口概览
| 项目 | 说明 |
| --- | --- |
| **生产环境** | `POST http://baishanyunpiao.com/prod-api/open/api/invoice` |
| **税控测试** | `POST http://baishanyunpiao.com/prod-api/open/api/sk/test/invoice` |
| **TOKEN** | 请求头需携带 `token: <你的 token>` |
| **功能** | 一次调用完成直连开票（专票/普票/电票/卷票/电专等），支持正数、负数（红冲）、自动拆分、异步/同步开具等 |

> ⚠️ 数电票暂无测试环境；纸质票只返回开票数据，需再调【发票打印】。  

---

## 2 业务请求参数（顶层）

| 字段 | 类型 | 必填 | 说明 |
| ---- | ---- | ---- | ---- |
| `taxNo` | String(20) | ✔ | 销方税号 |
| `taxUserName` | String(20) |  | 一税号多账户时区分登录名 |
| `invoiceTerminalCode` | String(30) |  | 开票终端代码（UKey/盘号等） |
| `isSplit` | Boolean |  | 是否超限拆分（仅 004/007/026） |
| `orgCode` | String(64) |  | 组织机构编码 |
| `isAsync` | String(1) |  | `0`同步 `1`异步（仅 RPA） |
| `isReturnRedInfo` | String(1) |  | `1` 返回蓝票已红冲信息 |
| `formatGenerate` | Boolean |  | 是否生成版式并返回链接 |
| `formatPushType` | Boolean |  | 版式生成是否推送 |
| `data` | **Object** | ✔ | 见下表「data 节点」 |

---

### 2.1 `data` 节点（关键字段）

| 字段 | 类型 | 必填 | 说明 |
| ---- | ---- | ---- | ---- |
| `invoiceTypeCode` | String(3) | ✔ | 004专 · 007普 · 026电 · 025卷 · 028电专 · 01全电专 · 02全电普 |
| `invoiceType` | String(1) |  | `0`蓝票 `1`红票 |
| `serialNo` | String(50) | ✔ | 开票流水号（幂等关键） |
| `buyerName` | String(80/100) | ✔ | 购方名称 |
| `buyerTaxNo` | String(20) | *条件* | 开专票 / 农产品收购 必填 |
| `invoiceDetailsList` | List\<Detail\> | ✔ | 商品明细，≤8 行否则需 `invoiceListMark=1` |
| `invoiceTotalPrice` | BigDecimal(13,2) |  | 合计金额 |
| `invoiceTotalTax` | BigDecimal(13,2) |  | 合计税额 |
| `invoiceTotalPriceTax` | BigDecimal(13,2) |  | 价税合计 |
| `remarks` | String(200/230) |  | 备注 |
| `redInfoNo / redConfirmUuid` | String | *红冲必填* | 税控用红字信息表编号 / 全电用确认单 UUID |

> 其余 100+ 字段请按需映射（差额征税、共同购买方、特定业务、整单折扣等）。完整枚举见原表。  

---

### 2.2 `invoiceDetailsList` 元素

| 字段 | 类型 | 必填 | 说明 |
| ---- | ---- | ---- | ---- |
| `goodsLineNo` | Integer | ✔ | 明细行号（连续、从 1 开始） |
| `goodsCode` | String(40) | ✔ | 税收分类编码（末级） |
| `goodsName` | String(100) | ✔ | 商品名称（与编码匹配） |
| `goodsTaxRate` | BigDecimal(5) | ✔ | 税率，例如 `0.13` |
| `goodsTotalPrice` | BigDecimal(13,2) | ✔ | 金额 |
| `goodsTotalTax` | BigDecimal(13,2) |  | 若为空自动计算 |
| `invoiceLineNature` | String(1) |  | `0`正常 `1`折扣 `2`被折扣 |
| `preferentialMarkFlag` | String(1) |  | `1`使用优惠政策时需填写 `vatSpecialManagement` |
| … | … | … | 单价、数量、单位、零税率标识等按规则选填 |

---

## 3 响应结构

| 字段 | 类型 | 含义 |
| ---- | ---- | ---- |
| `success` | List\<Result\> | 部分 / 全部开具成功信息 |
| `fail` | List\<Invoice\> | 拆分失败的金额明细 |
| **Result 对象** |  |  |
| ├ `invoiceCode` / `invoiceNo` | String | 发票代码 / 号码 |
| ├ `invoiceDate` | String(14) | `yyyyMMddHHmmss` |
| ├ `eInvoiceUrl` | String(1024) | 电子版式下载链接 |
| ├ `invoiceTotalPrice/Tax/PriceTax` | BigDecimal | 金额汇总 |
| └ `invoiceDetailsList` | List\<Detail\> | 回填明细 |

> 全电票不开票代码/校验码字段。  

---

## 4 常见错误码（节选）

| 代码 | 描述 |
| ---- | ---- |
| `00000004` | 没有 USB 设备 |
| `0009D101` | 超过单张金额限额 |
| `30507011` | 电子票不能开具清单发票 |
| `30507102` | 行号 n 单价×数量≠金额 |
| `70031` | 全电账号登录失效需重新认证 |
| `90048` | 合计金额+税额≠价税合计 |
| `99999` | 重复请求 |

> 详细错误码请参见附录列表，代码→提示直接做枚举到业务异常。  

---

## 5 请求示例（摘录）

```jsonc
POST /prod-api/open/api/invoice
Header: { "token": "YOUR_TOKEN" }

{
  "taxNo": "36996300000000013",
  "invoiceTerminalCode": "410123456725",
  "isSplit": false,
  "isReturnRedInfo": "1",
  "data": {
    "invoiceTypeCode": "026",
    "invoiceType": "0",
    "serialNo": "12345642asda",
    "buyerName": "百望测试40",
    "buyerTaxNo": "91500000747150890S",
    "invoiceTotalPrice": 10,
    "invoiceTotalTax": 1.3,
    "invoiceTotalPriceTax": 11.3,
    "buyerAddressPhone": "北京市海淀区 010-83332891",
    "drawer": "张一诺",
    "checker": "李佳佳",
    "payee": "任盈盈",
    "invoiceDetailsList": [
      {
        "goodsLineNo": 1,
        "goodsCode": "1010101070000000000",
        "goodsName": "燕麦",
        "goodsQuantity": 10,
        "goodsPrice": 1,
        "goodsTotalPrice": 10,
        "goodsTaxRate": 0.13,
        "goodsTotalTax": 1.3,
        "invoiceLineNature": "0"
      }
    ]
  }
}
```
  

---

## 6 成功响应示例（节选）

```json
{
  "success": [{
    "invoiceCode": "011000100011",
    "invoiceNo": "00576021",
    "invoiceDate": "20231126221828",
    "eInvoiceUrl": "https://www-pre.baiwang.com/fp/d?d=71CBB33D...",
    "invoiceTotalPrice": 10.00,
    "invoiceTotalTax": 1.30,
    "invoiceTotalPriceTax": 11.30,
    "serialNo": "12345642asda",
    "invoiceDetailsList": [
      { "goodsLineNo": 1, "goodsName": "*谷物*燕麦", "goodsTaxRate": 0.13 }
    ]
  }]
}
```
  

---

## 7 集成落地要点

1. **幂等性**：`serialNo` 必须唯一，重复提交会返回 `30504009/99999`。
2. **金额校验**：本地先计算两位小数，防止 `30507102/90046`。
3. **行数限制**：>8 行时必须带清单或拆分；电票不能清单。
4. **红冲流程**：税控需红字信息表，字段 `redInfoNo`；全电需确认单 `redConfirmUuid`。
5. **差额征税**：`taxationMethod=2` 且需传 `deductibleAmount` + `invoiceBalanceinfoList`。
6. **异常处理**：根据错误码做可重试、补偿或人工提示（全电 7xxxx 多为税局问题应提示“稍后重试或重新登录”）。

> 按以上结构映射实体类 / DTO，可直接驱动代码生成器或校验器，无需再翻找 PDF。