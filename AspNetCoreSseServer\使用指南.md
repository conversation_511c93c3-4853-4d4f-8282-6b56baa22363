# 心花商城 MCP 服务器使用指南

## 🚀 快速开始

### 1. 启动服务器
```cmd
# 方式一：使用启动脚本（推荐）
start_chinese.bat

# 方式二：构建并启动
build_and_start.bat

# 方式三：命令行启动
dotnet run --urls "http://localhost:5000"
```

### 2. 验证服务器
```powershell
# 简单测试（英文版，避免编码问题）
powershell -ExecutionPolicy Bypass -File test_simple.ps1

# 快速测试（需要服务器已启动）
powershell -ExecutionPolicy Bypass -File quick_test.ps1

# 完整测试（需要服务器已启动）
powershell -ExecutionPolicy Bypass -File test_tools_chinese.ps1

# 启动测试（自动启动服务器并测试）
powershell -ExecutionPolicy Bypass -File test_startup.ps1

# 系统诊断
powershell -ExecutionPolicy Bypass -File diagnose.ps1
```

### 3. 停止服务器
```cmd
stop_server.bat
```

## 🔧 可用工具详解

### 商品管理工具

#### 搜索商品
```http
GET /tools/call/SearchProductsAsync?keyword=手机&page=1&pageSize=10
```
- `keyword`: 搜索关键词（可选）
- `page`: 页码，从1开始
- `pageSize`: 每页数量

#### 获取商品详情
```http
GET /tools/call/GetProductByIdAsync?productId=12345678-1234-1234-1234-123456789012
```
- `productId`: 商品ID（GUID格式）

#### 获取低库存商品
```http
GET /tools/call/GetLowStockProductsAsync?threshold=10&page=1&pageSize=10
```
- `threshold`: 库存阈值
- `page`: 页码
- `pageSize`: 每页数量

### 订单管理工具

#### 搜索订单
```http
GET /tools/call/SearchOrdersAsync?keyword=ORD001&page=1&pageSize=10
```
- `keyword`: 订单号关键词（可选）
- `page`: 页码
- `pageSize`: 每页数量

#### 获取订单详情
```http
GET /tools/call/GetOrderByIdAsync?orderId=12345678-1234-1234-1234-123456789012
```
- `orderId`: 订单ID（GUID格式）

### 店铺管理工具

#### 搜索店铺
```http
GET /tools/call/SearchShopsAsync?keyword=花店&page=1&pageSize=10
```
- `keyword`: 店铺名称、联系人或电话（可选）
- `page`: 页码
- `pageSize`: 每页数量

#### 获取店铺详情
```http
GET /tools/call/GetShopByIdAsync?shopId=12345678-1234-1234-1234-123456789012
```
- `shopId`: 店铺ID（GUID格式）

### 用户管理工具

#### 搜索用户
```http
GET /tools/call/SearchUsersAsync?keyword=张三&page=1&pageSize=10
```
- `keyword`: 用户名、姓名、邮箱或手机号（可选）
- `page`: 页码
- `pageSize`: 每页数量

#### 获取用户详情
```http
GET /tools/call/GetUserByIdAsync?userId=12345
```
- `userId`: 用户ID（长整型）

### 基础工具

#### 回声测试
```http
GET /tools/call/Echo?message=你好世界
```
- `message`: 要回显的消息

#### 计算器
```http
GET /tools/call/Add?a=10&b=20
GET /tools/call/Subtract?a=30&b=10
GET /tools/call/Multiply?a=5&b=6
GET /tools/call/Divide?a=100&b=5
```
- `a`: 第一个数字
- `b`: 第二个数字

## 📊 返回数据格式

所有工具调用都返回统一的JSON格式：

```json
{
  "content": [
    {
      "type": "text",
      "text": "具体的返回内容（中文）"
    }
  ]
}
```

## 🔍 故障排除

### 常见问题

#### 1. 服务器启动失败
**症状**: 运行启动脚本后出现错误
**解决方案**:
- 检查端口5000是否被占用：`netstat -an | findstr :5000`
- 重新构建项目：运行 `build_and_start.bat`
- 检查数据库连接是否正常

#### 2. 工具调用返回错误
**症状**: API调用返回500错误或异常信息
**解决方案**:
- 检查参数格式是否正确（特别是GUID和数字类型）
- 确认数据库中存在相应的数据
- 查看服务器控制台输出的错误信息

#### 3. 数据库连接失败
**症状**: 启动时提示数据库连接错误
**解决方案**:
- 检查 `appsettings.json` 中的连接字符串
- 确认数据库服务器可访问
- 验证用户名和密码正确

#### 4. 端口占用
**症状**: 提示端口5000已被占用
**解决方案**:
- 运行 `stop_server.bat` 停止现有服务
- 或者修改启动命令使用其他端口：`dotnet run --urls "http://localhost:5001"`

### 调试技巧

#### 1. 查看详细日志
启动时添加详细日志级别：
```cmd
set ASPNETCORE_ENVIRONMENT=Development
dotnet run --urls "http://localhost:5000"
```

#### 2. 测试数据库连接
使用SQL Server Management Studio或其他工具连接到数据库：
```
Server: **********
Database: HeartFlowerMall-Test
User: heartflower
Password: jiushini@1qaz
```

#### 3. 检查工具注册
访问 `http://localhost:5000/tools/list` 查看所有已注册的工具。

## 📝 开发说明

### 添加新工具
1. 在 `Tools` 目录下创建新的工具类
2. 使用 `[McpServerToolType]` 标记类
3. 使用 `[McpServerTool]` 和 `[Description]` 标记方法
4. 在 `Program.cs` 中注册工具：`.WithTools<YourNewTool>()`

### 数据库操作
使用注入的仓储接口进行数据访问：
```csharp
private readonly IRepository<Entity, KeyType> _repository;

// 查询示例
var entity = await _repository.FirstOrDefaultAsync(e => e.Id == id);
var entities = await _repository.GetAllListAsync(e => e.Name.Contains(keyword));
```

### 错误处理
所有工具方法都应包含适当的错误处理：
```csharp
try
{
    // 业务逻辑
    return "成功结果";
}
catch (Exception ex)
{
    return $"操作失败: {ex.Message}";
}
```

## 🔗 相关链接

- [MCP 协议文档](https://modelcontextprotocol.io/)
- [ASP.NET Core 文档](https://docs.microsoft.com/aspnet/core/)
- [Entity Framework Core 文档](https://docs.microsoft.com/ef/core/)

## 📞 技术支持

如遇到问题，请检查：
1. 服务器控制台输出
2. 数据库连接状态
3. 网络连接情况
4. 参数格式正确性

更多详细信息请参考 `MIGRATION_COMPLETE.md` 文档。