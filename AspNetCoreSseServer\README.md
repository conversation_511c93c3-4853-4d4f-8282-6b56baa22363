# 心花商城 MCP 服务器

这是一个基于 ASP.NET Core 的 MCP (Model Context Protocol) 服务器实现，支持 SSE (Server-Sent Events) 传输协议，包含完整的心花商城业务工具集。

## 功能特性

### 中文化业务工具

1. **商品管理工具 (ProductTools)**
   - `GetProductByIdAsync` - 根据商品ID获取商品详细信息
   - `SearchProductsAsync` - 搜索商品信息，支持按关键词搜索
   - `GetLowStockProductsAsync` - 根据库存阈值获取低库存商品列表

2. **订单管理工具 (OrderTools)**
   - `SearchOrdersAsync` - 搜索订单信息，支持按订单号搜索
   - `GetOrderByIdAsync` - 根据订单ID获取订单详细信息，包括订单商品明细

3. **店铺管理工具 (ShopTools)**
   - `SearchShopsAsync` - 搜索店铺信息，支持按店铺名称、联系人或电话搜索
   - `GetShopByIdAsync` - 根据店铺ID获取店铺详细信息

4. **用户管理工具 (UserTools)**
   - `SearchUsersAsync` - 搜索用户信息，支持按用户名、姓名、邮箱或手机号搜索
   - `GetUserByIdAsync` - 根据用户ID获取用户详细信息

5. **计算器工具 (CalculatorTool)**
   - `Add` - 两个数字相加
   - `Subtract` - 第一个数字减去第二个数字
   - `Multiply` - 两个数字相乘
   - `Divide` - 第一个数字除以第二个数字

6. **回声工具 (EchoTool)**
   - `Echo` - 将输入的消息回显给客户端

7. **示例LLM工具 (SampleLlmTool)**
   - LLM 采样功能

### 数据库集成

- **Entity Framework Core** 与 SQL Server 集成
- **仓储模式** 实现数据访问
- **心花商城数据实体**：
  - User（用户）, Product（商品）, ProductStock（商品库存）
  - Order（订单）, OrderDetail（订单详情）, ShopList（店铺列表）, ShopCart（购物车）

### 配置特性

- 数据库连接配置在 `appsettings.json` 中
- 所有工具已注册到 MCP 服务器
- OpenTelemetry 集成用于监控

## 使用方法

### 启动服务器

#### 使用批处理文件启动
```cmd
start_chinese.bat
```

#### 使用 dotnet 命令启动
```bash
dotnet build
dotnet run --urls "http://localhost:5000"
```

服务器将在 `http://localhost:5000` 启动。

### 测试工具

运行测试脚本验证功能：

```powershell
# 测试基础功能
.\test_basic.ps1

# 测试所有工具（英文版）
.\test_tools.ps1

# 测试中文化工具
.\test_tools_chinese.ps1
```

## API 端点

- `GET /` - 服务器信息
- `GET /tools/list` - 获取可用工具列表
- `GET /tools/call/{toolName}` - 调用指定工具
- `GET /sse` - SSE 连接端点
- `POST /message` - JSON-RPC 消息处理端点

## 示例调用

### 搜索商品
```
GET /tools/call/SearchProductsAsync?keyword=手机&page=1&pageSize=10
```

### 获取订单详情
```
GET /tools/call/GetOrderByIdAsync?orderId=12345678-1234-1234-1234-123456789012
```

### 计算器
```
GET /tools/call/Add?a=10&b=20
```

### 回声测试
```
GET /tools/call/Echo?message=你好世界
```

## 数据库连接

应用程序使用 `appsettings.json` 中配置的连接字符串连接到心花商城数据库：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=47.95.15.9; Database=HeartFlowerMall-Test; User Id=heartflower; Password=*************; TrustServerCertificate=True; Encrypt=False;"
  }
}
```

确保数据库可访问并包含所需的表结构。

## 迁移说明

- 所有工具已适配使用 ModelContextProtocol.Server 属性
- 数据库访问使用仓储模式以提高可测试性
- 为所有工具操作实现了错误处理
- 工具描述和返回内容已中文化，提供更好的用户体验
- 保持与原有 MCP 框架的完全兼容性